// admin/js/aege-dashboard.js

jQuery(document).ready(function($) {
    // All of the code related to your charts and dashboard AJAX calls goes here.
    
    // Handle bulk update button
    $('#aege-bulk-update-btn').on('click', function() {
        var statusElement = $('#aege-bulk-update-status');
        var progressContainer = $('#aege-bulk-update-progress');
        var progressBar = $('#aege-bulk-update-progress-bar');
        var progressText = $('#aege-bulk-update-progress-text');
        
        progressContainer.show();
        statusElement.text('Initializing...');
        $(this).prop('disabled', true);
        
        getTotalPostCount(function(totalCount) {
            if (totalCount > 0) {
                processBatch(0, 50, totalCount, statusElement, $('#aege-bulk-update-btn'), progressContainer, progressBar, progressText);
            } else {
                statusElement.text('No AEGE-enabled posts found.');
                $('#aege-bulk-update-btn').prop('disabled', false);
            }
        });
    });

    // Initialize charts if they exist on the page
    initializeCharts();

    // Initialize dashboard charts
    function initializeCharts() {
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available, skipping chart initialization');
            return;
        }
        
        var schemaCtx = document.getElementById('schemaCoverageChart');
        if (schemaCtx) {
            new Chart(schemaCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['FAQ', 'How-To', 'Product', 'Review', 'Recipe', 'Event'],
                    datasets: [{
                        data: [
                            aege_ajax_object.schema_coverage.faq || 0,
                            aege_ajax_object.schema_coverage.howto || 0,
                            aege_ajax_object.schema_coverage.product || 0,
                            aege_ajax_object.schema_coverage.review || 0,
                            aege_ajax_object.schema_coverage.recipe || 0,
                            aege_ajax_object.schema_coverage.event || 0
                        ],
                        backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#E91E63', '#00BCD4']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        var citationCtx = document.getElementById('citationDensityChart');
        if (citationCtx) {
            new Chart(citationCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: ['Avg. Citations/Page', 'Pages with Citations', 'Total Citations'],
                    datasets: [{
                        label: 'Citation Metrics',
                        data: [
                            aege_ajax_object.citation_density.avg_citations_per_page || 0,
                            aege_ajax_object.citation_density.pages_with_citations || 0,
                            aege_ajax_object.citation_density.total_citations || 0
                        ],
                        backgroundColor: ['#4CAF50', '#2196F3', '#FF9800']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        var freshnessCtx = document.getElementById('freshnessTimelineChart');
        if (freshnessCtx) {
            new Chart(freshnessCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: aege_ajax_object.content_freshness.timeline_labels || [],
                    datasets: [{
                        label: 'Content Updates',
                        data: aege_ajax_object.content_freshness.timeline_data || [],
                        borderColor: '#0073aa',
                        backgroundColor: 'rgba(0,115, 170,0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }
    
    function getTotalPostCount(callback) {
        $.post(aege_ajax_object.ajax_url, {
            action: 'aege_get_post_count',
            nonce: aege_ajax_object.nonce
        }, function(response) {
            if (response.success) {
                callback(response.data.total);
            } else {
                callback(0);
            }
        });
    }
    
    function processBatch(offset, limit, totalCount, statusElement, bulkUpdateBtn, progressContainer, progressBar, progressText) {
        var processed = Math.min(offset, totalCount);
        var percentage = Math.round((processed / totalCount) * 100);
        progressBar.val(percentage);
        progressText.text(processed + ' of ' + totalCount + ' posts processed (' + percentage + '%)');
        
        $.post(aege_ajax_object.ajax_url, {
            action: 'aege_bulk_update_metadata',
            offset: offset,
            limit: limit,
            nonce: aege_ajax_object.nonce
        }, function(response) {
            if (response.success) {
                var data = response.data;
                statusElement.text(data.message);
                
                if (data.has_more) {
                    setTimeout(function() {
                        processBatch(data.next_offset, data.limit, totalCount, statusElement, bulkUpdateBtn, progressContainer, progressBar, progressText);
                    }, 500);
                } else {
                    progressBar.val(100);
                    progressText.text('Complete! Reloading dashboard...');
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                }
            } else {
                statusElement.text('Error: ' + (response.data.message || 'Unknown error'));
                bulkUpdateBtn.prop('disabled', false);
            }
        }).fail(function() {
            statusElement.text('Error: Request failed');
            bulkUpdateBtn.prop('disabled', false);
        });
    }
});
