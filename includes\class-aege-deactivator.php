<?php
class AEGE_Deactivator {
	public static function deactivate() {
		flush_rewrite_rules();
		
		// Clear all AEGE transients
		self::clear_all_transients();
		
		// Clear database cache table
		self::clear_database_cache();
	}
	
	/**
	 * Clear all AEGE transients from the database
	 */
	private static function clear_all_transients() {
		global $wpdb;
		
		// Delete all AEGE-related transients
		$wpdb->query(
			"DELETE FROM {$wpdb->options} 
			WHERE option_name LIKE '_transient_aege_%' 
			OR option_name LIKE '_transient_timeout_aege_%'"
		);
	}
	
	/**
	 * Clear database cache table
	 */
	private static function clear_database_cache() {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'aege_cache';
		
		// Clear all data from the table
		$wpdb->query("TRUNCATE TABLE {$table_name}");
	}
}
