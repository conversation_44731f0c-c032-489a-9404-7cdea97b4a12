<?php
/**
 * AEGE Citation Analyzer
 *
 * Analyzes citation density and statistics for AEGE-optimized content
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

class AEGE_Citation_Analyzer {
    
    /**
     * Analyze citation density across all AEGE-enabled content
     *
     * @return array Citation statistics
     */
    public function analyze_citation_density() {
        global $wpdb;
        
        $stats = array(
            'total_citations' => 0,
            'pages_with_citations' => 0,
            'avg_citations_per_page' => 0,
            'citation_distribution' => array()
        );
        
        // Fast, direct query for pre-calculated citation counts
        $counts = $wpdb->get_col("
            SELECT meta_value FROM $wpdb->postmeta 
            WHERE meta_key = '_aege_citation_count'
        ");
        
        $pages_with_citations = 0;
        $total_citations = 0;
        
        foreach ($counts as $count) {
            $count = (int)$count;
            $total_citations += $count;
            if ($count > 0) {
                $pages_with_citations++;
                
                // Track distribution
                if (!isset($stats['citation_distribution'][$count])) {
                    $stats['citation_distribution'][$count] = 0;
                }
                $stats['citation_distribution'][$count]++;
            }
        }
        
        $stats['total_citations'] = $total_citations;
        $stats['pages_with_citations'] = $pages_with_citations;
        
        // Calculate average citations per page
        $total_pages = count($counts);
        $stats['avg_citations_per_page'] = $total_pages > 0 ? 
            round($total_citations / $total_pages, 2) : 0;
            
        return $stats;
    }
    
    /**
     * Calculate citation count from content
     *
     * @param string $content The content to analyze
     * @return int Number of citations
     */
    public function calculate_citation_count($content) {
        return preg_match_all('/data-citation="true"/', $content);
    }
    
    /**
     * Get citation statistics for a specific post
     *
     * @param int $post_id The post ID
     * @return array Citation statistics for the post
     */
    public function get_post_citation_stats($post_id) {
        $citation_count = get_post_meta($post_id, '_aege_citation_count', true);
        return array(
            'citation_count' => $citation_count ? (int)$citation_count : 0
        );
    }
    
    /**
     * Get top cited pages
     *
     * @param int $limit Number of pages to return
     * @return array Top cited pages
     */
    public function get_top_cited_pages($limit = 10) {
        global $wpdb;
        
        $results = $wpdb->get_results($wpdb->prepare("
            SELECT post_id, meta_value as citation_count
            FROM $wpdb->postmeta 
            WHERE meta_key = '_aege_citation_count'
            AND meta_value > 0
            ORDER BY meta_value DESC
            LIMIT %d
        ", $limit));
        
        $pages = array();
        foreach ($results as $result) {
            $post = get_post($result->post_id);
            if ($post) {
                $pages[] = array(
                    'post_id' => $result->post_id,
                    'title' => $post->post_title,
                    'url' => get_permalink($result->post_id),
                    'citation_count' => (int)$result->citation_count
                );
            }
        }
        
        return $pages;
    }
}