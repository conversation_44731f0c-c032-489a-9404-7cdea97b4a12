<?php
/**
 * AEGE Content Cleaner
 *
 * Cleans and processes content for AEGE optimization
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

class AEGE_Content_Cleaner {
    
    /**
     * Clean and optimize content for AEGE
     *
     * @param string $content The content to clean
     * @return string The cleaned content
     */
    public function clean($content) {
        if (empty($content)) {
            AEGE_Logger::debug('Empty content provided to cleaner');
            return '';
        }

        // Store original content length for comparison
        $original_length = strlen($content);
        
        AEGE_Logger::debug('Starting content cleaning', array(
            'original_length' => $original_length
        ));

        // Define allowed HTML tags and attributes
        $allowed_html = array(
            'a' => array(
                'href' => array(),
                'title' => array(),
            ),
            'br' => array(),
            'em' => array(),
            'strong' => array(),
            'p' => array(),
            'ul' => array(),
            'ol' => array(),
            'li' => array(),
            'h1' => array(),
            'h2' => array(),
            'h3' => array(),
            'h4' => array(),
            'h5' => array(),
            'h6' => array(),
            'img' => array(
                'src' => array(),
                'alt' => array(),
                'width' => array(),
                'height' => array(),
            ),
            'blockquote' => array(
                'cite' => array(),
            ),
            'q' => array(
                'cite' => array(),
            ),
            'table' => array(),
            'thead' => array(),
            'tbody' => array(),
            'tfoot' => array(),
            'tr' => array(),
            'th' => array(),
            'td' => array(),
        );

        // Sanitize the content using wp_kses for robust HTML sanitization
        $content = wp_kses($content, $allowed_html);

        // Additional security sanitization
        $content = $this->sanitize_attributes($content);

        // Remove shortcodes
        $content = $this->remove_shortcodes($content);

        // Remove emojis
        $content = $this->remove_emojis($content);

        // Remove invisible characters
        $content = $this->remove_invisible_characters($content);

        // Normalize basic whitespace
        $content = $this->normalize_basic_whitespace($content);

        // Remove excessive whitespace while preserving paragraph breaks
        $content = preg_replace('/[ \\t]+/', ' ', $content);
        $content = preg_replace('/\\n\\s*\\n\\s*\\n+/', "\n\n", $content);

        // Remove empty tags
        $content = $this->remove_empty_tags($content);

        // Normalize whitespace around block elements
        $content = $this->normalize_block_elements($content);

        // Enhance outbound links with security attributes (Phase 1)
        $content = $this->enhance_outbound_links($content);

        // Mark citation links with semantic attributes (Phase 2)
        $options = get_option('aege_settings');
        if (isset($options['enable_citation_marking']) && $options['enable_citation_marking'] == 1) {
            $content = $this->mark_citations($content);
        }

        // Trim final content
        $content = trim($content);

        // Log cleaning statistics if debugging is enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $cleaned_length = strlen($content);
            $reduction = round((($original_length - $cleaned_length) / $original_length) * 100, 2);
            AEGE_Logger::debug('Content cleaning completed', array(
                'original_length' => $original_length,
                'cleaned_length' => $cleaned_length,
                'reduction_percentage' => $reduction
            ));
        }

        return $content;
    }
    
    /**
     * Detect schemas in content and generate schema markup
     *
     * @param string $content The content to analyze
     * @param int $post_id The post ID
     * @return array Array of detected schemas
     */
    public function detect_and_generate_schemas($content, $post_id = 0) {
        AEGE_Logger::debug('Starting schema detection', array(
            'post_id' => $post_id,
            'content_length' => strlen($content)
        ));
        
        $schemas = array();
        
        // Detect HowTo schema
        $howto_schema = $this->detect_howto_schema($content, $post_id);
        if ($howto_schema) {
            $schemas[] = $howto_schema;
            AEGE_Logger::debug('Detected HowTo schema', array('post_id' => $post_id));
        }
        
        // Detect FAQ schema
        $faq_schema = $this->detect_faq_schema($content);
        if ($faq_schema) {
            $schemas[] = $faq_schema;
            AEGE_Logger::debug('Detected FAQ schema', array('post_id' => $post_id));
        }
        
        // Detect Product schema
        $product_schema = $this->detect_product_schema($content);
        if ($product_schema) {
            $schemas[] = $product_schema;
            AEGE_Logger::debug('Detected Product schema', array('post_id' => $post_id));
        }
        
        // Detect Review schema
        $review_schema = $this->detect_review_schema($content);
        if ($review_schema) {
            $schemas[] = $review_schema;
            AEGE_Logger::debug('Detected Review schema', array('post_id' => $post_id));
        }
        
        // Detect ClaimReview schema
        $claimreview_schema = $this->detect_claimreview_schema($content);
        if ($claimreview_schema) {
            $schemas[] = $claimreview_schema;
            AEGE_Logger::debug('Detected ClaimReview schema', array('post_id' => $post_id));
        }
        
        // Detect Recipe schema
        $recipe_schema = $this->detect_recipe_schema($content);
        if ($recipe_schema) {
            $schemas[] = $recipe_schema;
            AEGE_Logger::debug('Detected Recipe schema', array('post_id' => $post_id));
        }
        
        // Detect Event schema
        $event_schema = $this->detect_event_schema($content);
        if ($event_schema) {
            $schemas[] = $event_schema;
            AEGE_Logger::debug('Detected Event schema', array('post_id' => $post_id));
        }
        
        AEGE_Logger::info('Schema detection completed', array(
            'post_id' => $post_id,
            'detected_schemas_count' => count($schemas)
        ));
        
        return $schemas;
    }
    
    /**
     * Detect HowTo schema in content
     *
     * @param string $content The content to analyze
     * @param int $post_id The post ID
     * @return array|false HowTo schema or false if not detected
     */
    private function detect_howto_schema($content, $post_id = 0) {
        // Get the post object if post_id is provided
        $post = null;
        if ($post_id) {
            $post = get_post($post_id);
        }
        
        $is_howto = false;
        
        // Define command verbs for checking headings
        $command_verbs = ['ask', 'search', 'give', 'remember', 'consider', 'create', 'build', 'find', 'choose', 'select', 'identify', 'determine', 'evaluate', 'compare', 'review', 'check', 'verify', 'test', 'try', 'use', 'apply', 'follow', 'implement', 'develop', 'establish', 'set', 'define', 'explain', 'describe', 'list', 'organize', 'prepare', 'gather', 'collect', 'analyze', 'interpret', 'understand', 'recognize', 'avoid', 'prevent', 'reduce', 'increase', 'improve', 'enhance', 'maintain', 'manage', 'handle', 'deal', 'solve', 'address', 'resolve', 'fix', 'repair', 'replace', 'update', 'upgrade', 'install', 'configure', 'setup', 'design', 'plan', 'schedule', 'budget', 'estimate', 'calculate', 'measure', 'record', 'document', 'report', 'present', 'communicate', 'collaborate', 'coordinate', 'lead', 'supervise', 'train', 'educate', 'teach', 'learn', 'study', 'research', 'investigate', 'explore', 'discover', 'invent', 'innovate', 'create', 'produce', 'manufacture', 'assemble', 'construct', 'design', 'engineer', 'program', 'code', 'develop', 'debug', 'test', 'deploy', 'maintain', 'optimize', 'secure', 'protect', 'backup', 'restore', 'recover', 'troubleshoot', 'diagnose', 'repair', 'upgrade', 'replace'];
        
        // Signal 1: Check the title
        if ($post && stripos($post->post_title, 'how to') !== false) {
            $is_howto = true;
        }
        
        // Signal 2: Check for the original rigid patterns
        $original_patterns = '/<h[2-6][^>]*>(.*?[Hh]ow to.*?|.*?[Ss]tep.*?|.*?\d+\.\s.*?)<\/h[2-6]>(.*?)(?=<h[2-6]|$)/s';
        $has_original_patterns = preg_match_all($original_patterns, $content, $matches);
        
        if ($has_original_patterns) {
            $is_howto = true;
        }
        
        // Signal 3: Look for command verbs in headings (more flexible approach)
        if (!$is_howto) {
            // Extract H2 headings
            if (preg_match_all('/<h[2][^>]*>(.*?)<\/h[2]>/i', $content, $h2_matches)) {
                $step_like_headings = 0;
                foreach ($h2_matches[1] as $heading) {
                    $clean_heading = trim(wp_strip_all_tags($heading));
                    $first_word = strtolower(explode(' ', $clean_heading)[0]);
                    if (in_array($first_word, $command_verbs)) {
                        $step_like_headings++;
                    }
                }
                
                // If there are enough "step-like" headings, consider it a how-to
                if ($step_like_headings >= 3) {
                    $is_howto = true;
                }
            }
        }
        
        // If we've determined it's a how-to, generate the schema
        if ($is_howto) {
            $steps = array();
            
            // Extract steps from the content
            if ($has_original_patterns) {
                // Use the original method for extracting steps
                foreach ($matches[0] as $index => $section) {
                    // Extract steps from ordered lists
                    if (preg_match_all('/<li[^>]*>(.*?)<\/li>/s', $section, $step_matches)) {
                        foreach ($step_matches[1] as $step_text) {
                            $steps[] = array(
                                '@type' => 'HowToStep',
                                'text' => wp_strip_all_tags($step_text)
                            );
                        }
                    }
                }
            } else {
                // Use a more general approach to find steps
                // Look for H2-H6 headings and their following content
                if (preg_match_all('/<h[2-6][^>]*>(.*?)<\/h[2-6]>(.*?)(?=<h[2-6]|$)/s', $content, $general_matches)) {
                    // Define command verbs for checking headings (redefined here to ensure availability)
                    $command_verbs = ['ask', 'search', 'give', 'remember', 'consider', 'create', 'build', 'find', 'choose', 'select', 'identify', 'determine', 'evaluate', 'compare', 'review', 'check', 'verify', 'test', 'try', 'use', 'apply', 'follow', 'implement', 'develop', 'establish', 'set', 'define', 'explain', 'describe', 'list', 'organize', 'prepare', 'gather', 'collect', 'analyze', 'interpret', 'understand', 'recognize', 'avoid', 'prevent', 'reduce', 'increase', 'improve', 'enhance', 'maintain', 'manage', 'handle', 'deal', 'solve', 'address', 'resolve', 'fix', 'repair', 'replace', 'update', 'upgrade', 'install', 'configure', 'setup', 'design', 'plan', 'schedule', 'budget', 'estimate', 'calculate', 'measure', 'record', 'document', 'report', 'present', 'communicate', 'collaborate', 'coordinate', 'lead', 'supervise', 'train', 'educate', 'teach', 'learn', 'study', 'research', 'investigate', 'explore', 'discover', 'invent', 'innovate', 'create', 'produce', 'manufacture', 'assemble', 'construct', 'design', 'engineer', 'program', 'code', 'develop', 'debug', 'test', 'deploy', 'maintain', 'optimize', 'secure', 'protect', 'backup', 'restore', 'recover', 'troubleshoot', 'diagnose', 'repair', 'upgrade', 'replace'];
                    
                    foreach ($general_matches[1] as $index => $heading) {
                        $heading_text = wp_strip_all_tags($heading);
                        $first_word = strtolower(explode(' ', trim($heading_text))[0]);
                        
                        // If the heading starts with a command verb, treat it as a step
                        if (in_array($first_word, $command_verbs)) {
                            $content_text = wp_strip_all_tags($general_matches[2][$index]);
                            // Trim the content to a reasonable length
                            $content_text = substr(trim($content_text), 0, 200);
                            if (!empty($content_text)) {
                                $steps[] = array(
                                    '@type' => 'HowToStep',
                                    'text' => $content_text
                                );
                            }
                        }
                    }
                }
            }
            
            if (!empty($steps)) {
                return array(
                    '@type' => 'HowTo',
                    'name' => $post ? $post->post_title : 'How to Instructions',
                    'step' => $steps
                );
            }
        }
        
        return false;
    }
    
    /**
     * Detect FAQ schema in content
     *
     * @param string $content The content to analyze
     * @return array|false FAQ schema or false if not detected
     */
    private function detect_faq_schema($content) {
        $questions = array();
        
        // Method 1: Look for question patterns in headings
        if (preg_match_all('/<h[2-6][^>]*>(.*?\\?.*?)<\/h[2-6]>(.*?)(?=<h[2-6]|$)/s', $content, $matches)) {
            foreach ($matches[1] as $index => $question) {
                $answer = isset($matches[2][$index]) ? wp_strip_all_tags($matches[2][$index]) : '';
                
                if (!empty($answer)) {
                    $questions[] = array(
                        '@type' => 'Question',
                        'name' => wp_strip_all_tags($question),
                        'acceptedAnswer' => array(
                            '@type' => 'Answer',
                            'text' => trim($answer)
                        )
                    );
                }
            }
        }
        
        // Method 2: Look for Q&A patterns that might not be in headings
        // Look for patterns like "Q:" or "Question:" followed by text, then "A:" or "Answer:"
        if (preg_match_all('/(?:[Qq]:|[Qq]uestion:)(.*?)(?:[Aa]:|[Aa]nswer:)(.*?)(?=[Qq]:|[Qq]uestion:|$)/s', $content, $qa_matches)) {
            foreach ($qa_matches[1] as $index => $question) {
                $answer = isset($qa_matches[2][$index]) ? wp_strip_all_tags($qa_matches[2][$index]) : '';
                $question = wp_strip_all_tags($question);
                
                // Clean up the question and answer
                $question = trim($question, " \t\n\r\0\x0B:?");
                $answer = trim($answer);
                
                if (!empty($question) && !empty($answer)) {
                    // Check if this question is already in our list to avoid duplicates
                    $already_added = false;
                    foreach ($questions as $existing_q) {
                        if ($existing_q['name'] === $question) {
                            $already_added = true;
                            break;
                        }
                    }
                    
                    if (!$already_added) {
                        $questions[] = array(
                            '@type' => 'Question',
                            'name' => $question,
                            'acceptedAnswer' => array(
                                '@type' => 'Answer',
                                'text' => $answer
                            )
                        );
                    }
                }
            }
        }
        
        // Method 3: Look for frequently asked questions section
        // Check if there's an FAQ section with multiple Q&A pairs
        if (stripos($content, 'faq') !== false || stripos($content, 'frequently asked') !== false) {
            // Look for any question mark followed by text, then another question mark or end of content
            if (preg_match_all('/([^\\.!?]*\\?[^\\.!?]*?)([\\.!?].*?)(?=[^\\.!?]*\\?|$)/s', $content, $faq_matches)) {
                foreach ($faq_matches[1] as $index => $potential_question) {
                    $potential_answer = isset($faq_matches[2][$index]) ? $faq_matches[2][$index] : '';
                    
                    // Clean and validate
                    $question = trim(wp_strip_all_tags($potential_question));
                    $answer = trim(wp_strip_all_tags($potential_answer));
                    
                    // Check if it looks like a real Q&A pair
                    if (strlen($question) > 5 && strlen($answer) > 10 && 
                        substr($question, -1) === '?' && 
                        stripos($answer, $question) === false) { // Make sure answer doesn't just repeat the question
                        
                        // Check if this question is already in our list to avoid duplicates
                        $already_added = false;
                        foreach ($questions as $existing_q) {
                            if (similar_text($existing_q['name'], $question) > 80) { // 80% similarity
                                $already_added = true;
                                break;
                            }
                        }
                        
                        if (!$already_added) {
                            $questions[] = array(
                                '@type' => 'Question',
                                'name' => $question,
                                'acceptedAnswer' => array(
                                    '@type' => 'Answer',
                                    'text' => $answer
                                )
                            );
                        }
                    }
                }
            }
        }
        
        if (!empty($questions)) {
            return array(
                '@type' => 'FAQPage',
                'mainEntity' => $questions
            );
        }
        
        return false;
    }
    
    /**
     * Detect Product schema in content
     *
     * @param string $content The content to analyze
     * @return array|false Product schema or false if not detected
     */
    private function detect_product_schema($content) {
        // Look for price patterns
        if (preg_match('/\$[\d,]+\.?\d*/', $content) || preg_match('/\bprice\b/i', $content)) {
            return array(
                '@type' => 'Product',
                'name' => 'Product',
                'description' => 'Product description'
            );
        }
        
        return false;
    }
    
    /**
     * Detect Review schema in content
     *
     * @param string $content The content to analyze
     * @return array|false Review schema or false if not detected
     */
    private function detect_review_schema($content) {
        // Look for review patterns
        if (preg_match('/\b(review|rating|stars)\b/i', $content) && 
            (preg_match('/\b(out of|\/|of)\s*\d+\b/', $content) || 
             preg_match('/★/', $content))) {
            return array(
                '@type' => 'Review',
                'reviewBody' => 'Review content',
                'reviewRating' => array(
                    '@type' => 'Rating',
                    'ratingValue' => 5,
                    'bestRating' => 5
                )
            );
        }
        
        return false;
    }
    
    /**
     * Detect ClaimReview schema in content
     *
     * @param string $content The content to analyze
     * @return array|false ClaimReview schema or false if not detected
     */
    private function detect_claimreview_schema($content) {
        // Look for claim review patterns
        if (preg_match('/\b(fact[\s-]?check|claim|verdict|debunk|myth|false|true|rating)\b/i', $content) &&
            (preg_match('/\b(claim|statement):\s*[\'"][^\'"]+[\'"]/', $content) || 
             preg_match('/\b(rating|verdict):\s*\w+/i', $content))) {
            return array(
                '@type' => 'ClaimReview',
                'claimReviewed' => 'Claim under review',
                'reviewRating' => array(
                    '@type' => 'Rating',
                    'ratingValue' => 1,
                    'bestRating' => 1,
                    'worstRating' => 1
                )
            );
        }
        
        return false;
    }
    
    /**
     * Detect Recipe schema in content
     *
     * @param string $content The content to analyze
     * @return array|false Recipe schema or false if not detected
     */
    private function detect_recipe_schema($content) {
        // Look for recipe patterns
        if (preg_match('/<h[1-6][^>]*>(.*?[Rr]ecipe.*?)<\/h[1-6]>/s', $content) ||
            preg_match('/\b(ingredients|prep time|cook time|servings)\b/i', $content)) {
            return array(
                '@type' => 'Recipe',
                'name' => 'Recipe',
                'recipeIngredient' => array(),
                'recipeInstructions' => array()
            );
        }
        
        return false;
    }
    
    /**
     * Detect Event schema in content
     *
     * @param string $content The content to analyze
     * @return array|false Event schema or false if not detected
     */
    private function detect_event_schema($content) {
        // Look for event patterns
        if (preg_match('/\b(event|concert|conference|webinar|workshop)\b/i', $content) &&
            (preg_match('/\b(\d{4}-\d{2}-\d{2}|\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b)/', $content) ||
             preg_match('/\b(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\b/', $content))) {
            return array(
                '@type' => 'Event',
                'name' => 'Event',
                'startDate' => date('c'),
                'eventAttendanceMode' => 'https://schema.org/OfflineEventAttendanceMode',
                'eventStatus' => 'https://schema.org/EventScheduled'
            );
        }
        
        return false;
    }
    
    
    
    /**
     * Enhance outbound links with security attributes
     *
     * @param string $content The content to process
     * @return string The content with enhanced outbound links
     */
    private function enhance_outbound_links($content) {
        // A more robust regex that handles single quotes and different attribute orders.
        $pattern = '/<a\s+(.*?)href=([\"\'])(.*?)\2(.*?)>/i';

        return preg_replace_callback($pattern, function($matches) {
            $url = $matches[3];

            // If it's not an outbound link, return the original anchor tag immediately.
            if (!$this->is_outbound_link($url)) {
                return $matches[0];
            }

            $full_match = $matches[0];
            $attributes_before = $matches[1];
            $attributes_after = $matches[4];
            
            // Check if a 'rel' attribute already exists in the entire tag.
            if (preg_match('/rel=/i', $full_match)) {
                // It exists, so we only add what's missing.
                $rel_values_to_add = [];
                if (strpos($full_match, 'noopener') === false) {
                    $rel_values_to_add[] = 'noopener';
                }
                if (strpos($full_match, 'noreferrer') === false) {
                    $rel_values_to_add[] = 'noreferrer';
                }

                if (!empty($rel_values_to_add)) {
                    // Add the new values to the existing rel attribute.
                    return preg_replace('/rel=([\"\'])(.*?)\1/i', 'rel="$2 ' . implode(' ', $rel_values_to_add) . '"', $full_match, 1);
                } else {
                    return $full_match; // Nothing to add.
                }
            } else {
                // No rel attribute exists, so add it.
                return '<a ' . $attributes_before . 'href="' . $url . '"' . $attributes_after . ' rel="noopener noreferrer">';
            }
        }, $content);
    }
    
    /**
     * Mark citation links with semantic attributes
     *
     * @param string $content The content to process
     * @return string The content with marked citation links
     */
    private function mark_citations($content) {
        // Get the detection mode from settings
        $options = get_option('aege_settings');
        $detection_mode = isset($options['citation_detection_mode']) ? $options['citation_detection_mode'] : 'automatic'; // Default to automatic

        if ($detection_mode === 'automatic') {
            // --- AUTOMATIC MODE LOGIC ---
            // Find ALL anchor tags
            $pattern = '/<a\\s+(.*?)>/i';

            return preg_replace_callback($pattern, function($matches) {
                $full_tag = $matches[0];
                $attributes = $matches[1];
                
                // 1. Check if it's an outbound link.
                // We need to extract the href for this.
                if (preg_match('/href=([\"\'])(.*?)\1/', $full_tag, $href_matches)) {
                    $url = $href_matches[2];
                    if (!$this->is_outbound_link($url)) {
                        return $full_tag; // It's internal, so we don't touch it.
                    }
                } else {
                    return $full_tag; // Not a valid link, don't touch it.
                }

                // --- START OF NEW, ENHANCED EXCLUSION LOGIC ---

                // 2. Check for explicit exclusion signals.
                $is_excluded = false;

                // Check for class="no-citation"
                if (strpos($attributes, 'no-citation') !== false) {
                    $is_excluded = true;
                }

                // Check for rel="sponsored" or rel="ugc"
                if (!$is_excluded && preg_match('/rel=(["\']).*?(sponsored|ugc).*?\1/', $full_tag)) {
                    $is_excluded = true;
                }

                if ($is_excluded) {
                    return $full_tag; // User has opted this link out, so we don't touch it.
                }

                // --- END OF NEW, ENHANCED EXCLUSION LOGIC ---

                // 3. It's an outbound link and not excluded, so add the data-citation attribute.
                if (strpos($attributes, 'data-citation') === false) {
                    return '<a data-citation="true" ' . $attributes . '>';
                }

                return $full_tag; // Already has the attribute.
            }, $content);

        } else {
            // --- MANUAL MODE LOGIC (Your existing, excellent code) ---
            // Find only anchor tags that have the "citation" class.
            $pattern = '/<a\\s+(.*?)class=([\"\'])(.*?\\bcitation\\b.*?)\\2(.*?)>/i';

            return preg_replace_callback($pattern, function($matches) {
                $full_match = $matches[0];
                if (strpos($full_match, 'data-citation') === false) {
                    return str_replace('<a ', '<a data-citation=\"true\" ', $full_match);
                }
                return $full_match;
            }, $content);
        }
    }
    
    /**
     * Remove shortcodes from content
     *
     * @param string $content The content to process
     * @return string The content with shortcodes removed
     */
    private function remove_shortcodes($content) {
        // Remove all shortcodes using WordPress built-in function
        return strip_shortcodes($content);
    }

    /**
     * Remove emojis from content
     *
     * @param string $content The content to process
     * @return string The content with emojis removed
     */
    private function remove_emojis($content) {
        // Remove emoji characters using regex
        return preg_replace('/[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]/u', '', $content);
    }

    /**
     * Remove invisible characters from content
     *
     * @param string $content The content to process
     * @return string The content with invisible characters removed
     */
    private function remove_invisible_characters($content) {
        // Remove zero-width spaces, non-breaking spaces, and other invisible characters
        $invisible_chars = array(
            "\xE2\x80\x8B", // Zero-width space
            "\xE2\x80\x8C", // Zero-width non-joiner
            "\xE2\x80\x8D", // Zero-width joiner
            "\xEF\xBB\xBF", // Byte order mark
            "\xC2\xA0",     // Non-breaking space
        );

        return str_replace($invisible_chars, '', $content);
    }

    /**
     * Normalize basic whitespace in content
     *
     * @param string $content The content to process
     * @return string The content with normalized whitespace
     */
    private function normalize_basic_whitespace($content) {
        // Replace multiple spaces with single space
        $content = preg_replace('/[ \t]+/', ' ', $content);

        // Normalize line breaks
        $content = preg_replace('/\r\n|\r/', "\n", $content);

        // Remove trailing whitespace from lines
        $content = preg_replace('/[ \t]+$/m', '', $content);

        return $content;
    }

    /**
     * Remove empty HTML tags from content
     *
     * @param string $content The content to process
     * @return string The content with empty tags removed
     */
    private function remove_empty_tags($content) {
        // Remove empty tags (but preserve self-closing tags like <br>, <img>)
        $empty_tags_pattern = '/<(?!(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)\s*\/?>)[^>]+>\s*<\/[^>]+>/';

        do {
            $content_before = $content;
            $content = preg_replace($empty_tags_pattern, '', $content);
        } while ($content !== $content_before);

        return $content;
    }

    /**
     * Normalize whitespace around block elements
     *
     * @param string $content The content to process
     * @return string The content with normalized block element spacing
     */
    private function normalize_block_elements($content) {
        // Add proper spacing around block elements
        $block_elements = array('p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'blockquote');

        foreach ($block_elements as $element) {
            // Add newlines before opening tags
            $content = preg_replace('/<' . $element . '([^>]*)>/', "\n<" . $element . '$1>', $content);

            // Add newlines after closing tags
            $content = preg_replace('/<\/' . $element . '>/', '</' . $element . ">\n", $content);
        }

        // Clean up excessive newlines
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        return trim($content);
    }

    /**
     * Additional security sanitization for HTML attributes
     *
     * @param string $content The content to sanitize
     * @return string The sanitized content
     */
    public function sanitize_attributes($content) {
        // Remove potentially dangerous attributes while preserving semantic ones
        $dangerous_attributes = array(
            'on\w+', // All on* event handlers (onclick, onload, etc.)
            'data-[^=]*', // Data attributes (except data-citation which we use)
            'style', // Style attributes
            'id' // ID attributes (usually not needed for LLM processing)
        );
        
        foreach ($dangerous_attributes as $attr) {
            $content = preg_replace('/\s*' . $attr . '=["\'][^"\']*["\']/i', '', $content);
        }
        
        // Handle class attributes more carefully - remove only dangerous classes
        $content = preg_replace_callback('/\s+class=(["\'])([^"\']*)\1/i', function($matches) {
            $classes = $matches[2];
            // Remove classes that might be used for tracking or styling that's not semantic
            $dangerous_classes = array(
                'google', 'facebook', 'twitter', 'social', 'tracking', 
                'ad', 'advertisement', 'sponsor', 'promo'
            );
            
            $class_list = explode(' ', $classes);
            $safe_classes = array();
            
            foreach ($class_list as $class) {
                $is_dangerous = false;
                foreach ($dangerous_classes as $dangerous) {
                    if (stripos($class, $dangerous) !== false) {
                        $is_dangerous = true;
                        break;
                    }
                }
                
                if (!$is_dangerous) {
                    $safe_classes[] = $class;
                }
            }
            
            if (empty($safe_classes)) {
                return ''; // Remove the entire class attribute if no safe classes remain
            }
            
            return ' class="' . implode(' ', $safe_classes) . '"';
        }, $content);
        
        return $content;
    }
    
    /**
     * Check if a URL is an outbound link
     *
     * @param string $url The URL to check
     * @return bool True if outbound, false if internal
     */
    private function is_outbound_link($url) {
        $site_url = parse_url(home_url(), PHP_URL_HOST);
        $link_url = parse_url($url, PHP_URL_HOST);
        return $link_url && $link_url !== $site_url;
    }
    
    /**
     * Trim content to a specific word count
     *
     * @param string $content The content to trim
     * @param int $word_count The maximum number of words
     * @param string $more The text to append if content is trimmed
     * @return string The trimmed content
     */
    public function trim_words($content, $word_count = 55, $more = '...') {
        return wp_trim_words($content, $word_count, $more);
    }
    
    /**
     * Extract text from HTML content
     *
     * @param string $content The HTML content
     * @return string The extracted text
     */
    public function extract_text($content) {
        return strip_tags($content);
    }
}