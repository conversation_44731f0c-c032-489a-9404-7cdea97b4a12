<?php
/**
 * AEGE SEO Base Integration
 *
 * Abstract base class for SEO plugin integrations
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Abstract base class for SEO plugin integrations
 */
abstract class AEGE_SEO_Base {
    
    /**
     * Initialize the integration
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks for the integration
     */
    protected function init_hooks() {
        // Add rewrite rules for our sitemap
        add_action('init', array($this, 'add_rewrite_rules'), 10);
        
        // Handle direct sitemap requests
        add_action('init', array($this, 'maybe_generate_sitemap'), 999);
        
        // Clear sitemap cache when AEGE content changes
        add_action('aege_cache_cleared', array($this, 'clear_sitemap_cache'));
        add_action('aege_object_cache_cleared', array($this, 'clear_sitemap_cache'));
        add_action('save_post', array($this, 'clear_sitemap_cache_on_post_save'));
    }
    
    /**
     * Add rewrite rules for our sitemap
     */
    public function add_rewrite_rules() {
        // Add rule for aege-sitemap.xml
        add_rewrite_rule('^aege-sitemap\\.xml$', 'index.php?aege_sitemap=1', 'top');
    }
    
    /**
     * Handle direct sitemap requests
     */
    public function maybe_generate_sitemap() {
        if (isset($_SERVER['REQUEST_URI']) && 
            (strpos(sanitize_text_field(wp_unslash($_SERVER['REQUEST_URI'])), 'aege-sitemap.xml') !== false || 
             strpos(sanitize_text_field(wp_unslash($_SERVER['REQUEST_URI'])), 'aege_sitemap.xml') !== false)) {
            
            status_header(200);
            header('Content-Type: application/xml; charset=utf-8');
            echo $this->generate_sitemap();
            exit;
        }
    }
    
    /**
     * Clear sitemap cache when AEGE cache is cleared
     */
    public function clear_sitemap_cache() {
        // This method should be overridden by child classes
    }
    
    /**
     * Clear sitemap cache when a post is saved
     */
    public function clear_sitemap_cache_on_post_save($post_id) {
        // Verify this is not an autosave or revision
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (wp_is_post_revision($post_id)) {
            return;
        }
        
        // Clear the sitemap cache
        $this->clear_sitemap_cache();
    }
    
    /**
     * Generate sitemap content
     *
     * @return string Sitemap XML content
     */
    abstract public function generate_sitemap();
    
    /**
     * Import schema data from the SEO plugin
     *
     * @param WP_Post $post The post object
     * @return array Schema data
     */
    abstract protected function import_schema_data($post);
    
    /**
     * Get the plugin name
     *
     * @return string Plugin name
     */
    abstract protected function get_plugin_name();
}