<?php
/**
 * AEGE Database Cache Manager
 *
 * Manages caching of AEGE content in a dedicated database table for better performance
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

class AEGE_Database_Cache {
    
    /**
     * Get cached content for a specific object
     *
     * @param int $object_id The ID of the object (post, term, etc.)
     * @param string $object_type The type of object ('post', 'term', etc.)
     * @return array|false The cached data or false if not found
     */
    public static function get_cached_content($object_id, $object_type) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        // Check if table exists
        if (!self::table_exists($table_name)) {
            AEGE_Logger::warning('Cache table does not exist', array('table_name' => $table_name));
            return false;
        }
        
        AEGE_Logger::debug('Querying database cache', array(
            'object_id' => $object_id,
            'object_type' => $object_type
        ));
        
        $cached_data = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE object_id = %d AND object_type = %s",
            $object_id,
            $object_type
        ), ARRAY_A);
        
        // Check if cache is expired
        if ($cached_data && isset($cached_data['last_updated'])) {
            $last_updated = strtotime($cached_data['last_updated']);
            $now = time();
            
            // Get cache duration from settings, default to 24 hours
            $options = get_option('aege_settings');
            $cache_strategy = isset($options['cache_strategy']) ? $options['cache_strategy'] : 'smart_cache';
            
            switch ($cache_strategy) {
                case 'aggressive_cache':
                    $cache_duration = 7 * DAY_IN_SECONDS; // 1 week
                    break;
                case 'no_cache':
                    $cache_duration = MINUTE_IN_SECONDS; // 1 minute
                    break;
                case 'smart_cache':
                default:
                    $cache_duration = 24 * HOUR_IN_SECONDS; // 24 hours
                    break;
            }
            
            if (($now - $last_updated) > $cache_duration) {
                // Cache is expired, clear it
                AEGE_Logger::debug('Cache expired, clearing', array(
                    'object_id' => $object_id,
                    'object_type' => $object_type,
                    'last_updated' => $last_updated,
                    'now' => $now,
                    'cache_duration' => $cache_duration
                ));
                self::clear_cache_for_object($object_id, $object_type);
                return false;
            }
        }
        
        if ($cached_data) {
            AEGE_Logger::debug('Database cache hit', array(
                'object_id' => $object_id,
                'object_type' => $object_type
            ));
        } else {
            AEGE_Logger::debug('Database cache miss', array(
                'object_id' => $object_id,
                'object_type' => $object_type
            ));
        }
        
        return $cached_data ? $cached_data : false;
    }
    
    /**
     * Save content to cache
     *
     * @param int $object_id The ID of the object
     * @param string $object_type The type of object
     * @param array $data The data to cache
     * @return bool True on success, false on failure
     */
    public static function save_to_cache($object_id, $object_type, $data, $cache_key) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        AEGE_Logger::debug('Saving to database cache', array(
            'object_id' => $object_id,
            'object_type' => $object_type,
            'cache_key' => $cache_key,
            'data_keys' => array_keys($data)
        ));
        
        // Prepare data for insertion
        $insert_data = array(
            'object_id' => $object_id,
            'object_type' => $object_type,
            'cache_key' => $cache_key,
            'content' => isset($data['content']) ? $data['content'] : '',
            'meta' => isset($data['meta']) ? $data['meta'] : '',
            'excerpts' => isset($data['excerpts']) ? $data['excerpts'] : '',
            'overview' => isset($data['overview']) ? $data['overview'] : '',
            'taxonomies' => isset($data['taxonomies']) ? $data['taxonomies'] : '',
            'title' => isset($data['title']) ? $data['title'] : '',
            'link' => isset($data['link']) ? $data['link'] : '',
            'sku' => isset($data['sku']) ? $data['sku'] : '',
            'price' => isset($data['price']) ? $data['price'] : '',
            'published' => isset($data['published']) ? $data['published'] : null,
            'modified' => isset($data['modified']) ? $data['modified'] : null,
            'last_updated' => current_time('mysql')
        );
        
        // Insert or update
        $result = $wpdb->replace($table_name, $insert_data, array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'));
        
        if ($result !== false) {
            AEGE_Logger::debug('Successfully saved to database cache', array(
                'object_id' => $object_id,
                'object_type' => $object_type,
                'cache_key' => $cache_key
            ));
        } else {
            AEGE_Logger::error('Failed to save to database cache', array(
                'object_id' => $object_id,
                'object_type' => $object_type,
                'cache_key' => $cache_key,
                'wpdb_error' => $wpdb->last_error
            ));
        }
        
        return $result !== false;
    }
    
    /**
     * Clear cache for a specific object
     *
     * @param int $object_id The ID of the object
     * @param string $object_type The type of object
     * @return bool True on success, false on failure
     */
    public static function clear_cache_for_object($object_id, $object_type) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        $result = $wpdb->delete($table_name, array(
            'object_id' => $object_id,
            'object_type' => $object_type
        ), array('%d', '%s'));
        
        return $result !== false;
    }
    
    /**
     * Clear all AEGE cache
     *
     * @return bool True on success, false on failure
     */
    public static function clear_all_cache() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        $result = $wpdb->query("TRUNCATE TABLE {$table_name}");
        
        return $result !== false;
    }
    
    /**
     * Get all cached objects of a specific type
     *
     * @param string $object_type The type of object to retrieve
     * @param int $limit Maximum number of results (0 for all)
     * @return array Array of cached objects
     */
    public static function get_cached_objects_by_type($object_type, $limit = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE object_type = %s ORDER BY modified DESC",
            $object_type
        );
        
        if ($limit > 0) {
            $query .= $wpdb->prepare(" LIMIT %d", $limit);
        }
        
        return $wpdb->get_results($query, ARRAY_A);
    }
    
    /**
     * Get count of cached objects by type
     *
     * @param string $object_type The type of object to count
     * @return int Number of cached objects
     */
    public static function get_cached_count_by_type($object_type) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        return (int) $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE object_type = %s",
            $object_type
        ));
    }
    
    /**
     * Delete expired cache entries (older than 30 days)
     *
     * @return int Number of deleted entries
     */
    public static function delete_expired_cache() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        $thirty_days_ago = date('Y-m-d H:i:s', strtotime('-30 days'));
        
        return $wpdb->query($wpdb->prepare(
            "DELETE FROM {$table_name} WHERE last_updated < %s",
            $thirty_days_ago
        ));
    }
    
    /**
     * Optimize the cache table
     *
     * @return bool True on success, false on failure
     */
    public static function optimize_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aege_cache';
        
        $result = $wpdb->query("OPTIMIZE TABLE {$table_name}");
        
        return $result !== false;
    }
    
    /**
     * Check if a table exists in the database
     *
     * @param string $table_name The table name to check
     * @return bool True if table exists, false otherwise
     */
    private static function table_exists($table_name) {
        global $wpdb;
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SHOW TABLES LIKE %s",
            $table_name
        ));
        
        return $result === $table_name;
    }
}
