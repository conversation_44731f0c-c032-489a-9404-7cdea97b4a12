<?php
/**
 * AEGE Entity Analyzer
 *
 * Detects named entities in content for AEO/GEO scoring
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

class AEGE_Entity_Analyzer {
    
    /**
     * Count named entities in content
     *
     * @param string $content The content to analyze
     * @return int Number of named entities detected
     */
    public function count_entities($content) {
        // Handle empty or non-string content
        if (empty($content) || !is_string($content)) {
            return 0;
        }
        
        // Remove HTML tags for cleaner processing
        $text = wp_strip_all_tags($content);
        
        // If text is empty after stripping tags, return 0
        if (empty($text)) {
            return 0;
        }
        
        // Pattern to match potential named entities:
        // - Proper nouns (capitalized words not at sentence start)
        // - Acronyms (all caps words 2-6 characters)
        // - Quoted entities
        $entity_patterns = [
            // Proper nouns: capitalized words that aren't at sentence start
            '/(?<!^|[.!?]\s)(?<!\s)(?<!\band\s)(?<!\bthe\s)(?<!\ba\s)(?<!\ban\s)(?<!\bor\s)(?<!\bbut\s)(?<!\bfor\s)(?<!\bof\s)(?<!\bat\s)(?<!\bby\s)(?<!\bin\s)(?<!\bto\s)(?<!\bfrom\s)(?<!\bwith\s)(?<!\bthrough\s)(?<!\bduring\s)(?<!\bbefore\s)(?<!\bafter\s)(?<!\babove\s)(?<!\bbelow\s)(?<!\bbetween\s)(?<!\bamong\s)(?<!\bunder\s)(?<!\buntil\s)(?<!\bwhile\s)(?<!\bwithin\s)(?<!\bwithout\s)(?<!\bagainst\s)(?<!\balong\s)(?<!\bacross\s)(?<!\bbehind\s)(?<!\bbeside\s)(?<!\bbeyond\s)(?<!\bnear\s)(?<!\bpast\s)(?<!\bsince\s)(?<!\bthan\s)(?<!\bvia\s)\b[A-Z][a-z]{2,}(?!\s)/',
            
            // Acronyms: 2-6 consecutive uppercase letters
            '/\b[A-Z]{2,6}\b/',
            
            // Quoted entities
            '/["\']([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)["\']/',
        ];
        
        $entities = [];
        
        foreach ($entity_patterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                foreach ($matches[0] as $match) {
                    // Clean the match
                    $clean_match = trim($match, ' .,;:!?()[]{}"\'');
                    
                    // Filter out common non-entities
                    if ($this->is_valid_entity($clean_match)) {
                        $entities[] = $clean_match;
                    }
                }
            }
        }
        
        // Remove duplicates and return count
        return count(array_unique($entities));
    }
    
    /**
     * Check if a potential entity is valid
     *
     * @param string $entity The potential entity
     * @return bool True if valid entity
     */
    private function is_valid_entity($entity) {
        // Too short
        if (strlen($entity) < 3) {
            return false;
        }
        
        // Common words that aren't entities
        $common_words = [
            'The', 'And', 'For', 'Are', 'But', 'Not', 'You', 'All', 'Can', 'Had', 'Her', 'Was', 'One', 'Our', 'Out', 'Day', 'Get', 'Has', 'Him', 'His', 'How', 'Its', 'May', 'New', 'Now', 'Old', 'See', 'Two', 'Who', 'Boy', 'Did', 'Man', 'Men', 'Run', 'Any', 'Big', 'End', 'Far', 'Got', 'Guy', 'Hot', 'Let', 'Lot', 'Put', 'Say', 'She', 'Too', 'Try', 'Us', 'Use', 'Way', 'Win', 'Yes', 'Yet', 'Bit', 'Car', 'Cut', 'Dad', 'Die', 'Eat', 'Fun', 'God', 'Hit', 'Job', 'Key', 'Law', 'Lay', 'Led', 'Lie', 'Log', 'Lot', 'Map', 'Net', 'Pay', 'Pot', 'Raw', 'Red', 'Row', 'Rub', 'Set', 'Sit', 'Top', 'Toy', 'War', 'Wet', 'Won'
        ];
        
        // Common prepositions/conjunctions
        $common_prepositions = [
            'In', 'On', 'At', 'By', 'To', 'Of', 'Up', 'As', 'Be', 'Do', 'Go', 'If', 'Is', 'It', 'Me', 'My', 'No', 'So', 'Up', 'Us', 'We'
        ];
        
        // Months
        $months = [
            'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        // Days
        $days = [
            'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
        ];
        
        // Combine all common words
        $all_common = array_merge($common_words, $common_prepositions, $months, $days);
        
        // Check if entity is in common words list
        if (in_array($entity, $all_common)) {
            return false;
        }
        
        // Check if it's mostly numbers
        if (preg_match('/^\d+$/', $entity)) {
            return false;
        }
        
        // Check if it's a mix of letters and numbers without clear pattern
        if (preg_match('/[a-zA-Z]+\d+|\d+[a-zA-Z]+/', $entity) && !preg_match('/^[A-Z]{2,6}$/', $entity)) {
            return false;
        }
        
        return true;
    }
}