=== AEGE - Answer Engine and Generative Engine Optimizer ===
Contributors: [Your Name/Company]
Tags: ai, seo, content optimization, answer engine, generative engine
Requires at least: 5.0
Tested up to: 6.0
Stable tag: 1.1.5
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

The definitive plugin for optimizing your content for Answer Engines and Generative Engines.

== Description ==

AEGE (Answer Engine and Generative Engine) is a sophisticated WordPress plugin designed to optimize your content for AI-driven platforms including search engines and large language models (LLMs). It creates AI-friendly versions of your content accessible through `/llm/` endpoints and provides tools for AI content discovery and management.

Key Features:

* **AEO (Answer Engine Optimization) Content Generation**: Automatically creates simplified, structured versions of your content specifically designed for AI systems and search engines.

* **Flexible Workflow Management**: Choose between automated processing with manual override or manual opt-in only workflows.

* **Create/Update LLM Button**: Update only the AI-optimized version of content without modifying the main post/page.

* **Content Summarization**: Automatically generates key takeaways that appear at the top of the LLM version to help AI systems quickly understand main points.

* **Database Caching**: Improves performance by caching generated AEGE content in a custom database table.

* **SEO Plugin Integration**: Seamless integration with popular SEO plugins for schema and sitemap compatibility.

* **Schema Detection and Enhancement**: Automatically identifies and structures content for better AI understanding through Schema.org markup.

* **llms.txt File Management**: Creates a discovery file that helps AI crawlers find all AEGE-optimized content.

* **Citation & Source Enhancement**: Enhances outbound links with security attributes and allows marking of citation links with semantic markup.

* **AEO Score Column**: Adds a sortable AEO Score column to your Posts and Pages lists for quick assessment of content optimization.

* **REST API**: Provides programmatic access to AEGE functionality for external integrations.

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/aege` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Use the AEGE Optimizer screen to configure the plugin

== Changelog ==

= 1.1.6 =
* Enhanced AEO Score column tooltip with professional Yoast-style design
* Improved tooltip positioning and hover effects for better user experience

= 1.1.5 =
* Added Automatic Citation Detection feature
* Implemented two citation detection modes: Automatic (default) and Manual
* Automatic mode treats all external links as citations unless excluded with class="no-citation"
* Manual mode preserves existing behavior requiring class="citation" for citation counting
* Enhanced citation documentation and user guidance
* Improved AJAX architecture for batch processing operations
* Moved JavaScript to external files for better maintainability
* Fixed SQL injection vulnerabilities in post counting functions
* Unified AJAX handlers for better code organization
* Added sortable AEO Score column to Posts and Pages lists with color-coded scores and tooltip information

= 1.1.4 =
* Enhanced Content Analysis Dashboard to AEO/GEO Scorecard
* Added Schema Coverage metrics with visualization
* Added Citation Density metrics with visualization
* Added AEO Content Freshness metrics with timeline visualization
* Optimized post counting with direct SQL queries for better performance
* Implemented debounced llms.txt regeneration for better performance
* Pre-calculated metrics on save for instant dashboard loading

= 1.1.3 =
* Fixed PHP warnings related to header modification
* Moved llms.txt handling to earlier WordPress hook to prevent headers already sent errors
* Moved log viewing functionality to admin_init hook to prevent headers already sent errors

= 1.0.0 =
* Initial release
* AEO Content Generation
* Workflow Management
* Create/Update LLM Button
* Content Summarization
* Database Caching
* SEO Plugin Integration
* Schema Detection and Enhancement
* llms.txt File Management
* REST API

== Upgrade Notice ==

= 1.1.6 =
Enhanced AEO Score column with professional "llm" text header instead of icon for improved reliability and integration

= 1.1.5 =
Added sortable AEO Score column to Posts and Pages lists with color-coded scores and tooltip information for quick content optimization assessment

= 1.1.4 =
Enhanced Content Analysis Dashboard with AEO/GEO Scorecard featuring Schema Coverage, Citation Density, and Content Freshness metrics with visualizations

= 1.1.3 =
Fixed PHP warnings related to header modification issues that could occur when viewing logs or accessing llms.txt

== Frequently Asked Questions ==

= How do I mark citations in my content? =
**Automatic Mode (Default)**:
By default, AEGE intelligently marks all external links as citations. To prevent a specific link from being counted (e.g., an affiliate link), simply add `class="no-citation"` to the anchor tag:

`<a href="https://example.com" class="no-citation">Affiliate Link</a>`

**Manual Mode**:
To mark a link as a citation, simply add the CSS class "citation" to the link in your content editor:

`<a href="https://example.com" class="citation">Example Source</a>`

Then enable the "Enable Citation Marking" option in the AEGE settings.

= How does the AEO Score column work? =

The AEO Score column appears in your Posts and Pages lists, showing a color-coded percentage score for each piece of content. The score is calculated based on schema coverage, citation density, and content freshness. Green scores (75-100) indicate well-optimized content, orange scores (50-74) indicate content that could be improved, and red scores (0-49) indicate content that needs optimization. Clicking on any score takes you to the detailed analysis page for that content.

The column header now features professional "llm" text instead of an icon, providing a cleaner and more reliable interface that integrates seamlessly with the WordPress admin design. This text header is styled with semibold formatting and proper alignment with sorting arrows. The column width has been increased to 75px to prevent text cutoff.

= How does the citation enhancement improve security? =

The plugin automatically adds `rel="noopener noreferrer"` attributes to all outbound links, which helps prevent tabnabbing attacks and protects user privacy.

== Screenshots ==

1. AEGE Settings Panel
2. Post Edit Screen with AEGE Metabox
3. AEGE-Optimized Content Example
4. AEO Score Column in Posts List

== Copyright ==

AEGE WordPress Plugin
Copyright (C) [Year] [Your Name/Company]

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License along
with this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.