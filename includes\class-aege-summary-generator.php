<?php
/**
 * AEGE Summary Generator
 *
 * Generates key takeaways and summaries for AEGE-optimized content
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}


class AEGE_Summary_Generator {

    private $options;

    public function __construct() {
        $this->options = get_option('aege_settings');
    }
    
    /**
     * Generate key takeaways for content
     *
     * @param string $content The content to analyze
     * @return array Array of key takeaways
     */
    public function generate_key_takeaways($content) {
        $takeaways = array();
        
        $max_points = $this->options['summary_max_key_points'] ?? 5;

        // Method 1: Extract important headings
        $headings = $this->extract_from_headings($content);
        if (!empty($headings)) {
            $takeaways['key_sections'] = $headings; // Use all headings, not just first 2
        }
        
        // Method 2: Extract list items (often key points)
        $list_items = $this->extract_list_items($content);
        if (!empty($list_items)) {
            $takeaways['key_points'] = array_slice($list_items, 0, $max_points);
        }
        
        // Method 3: Extract key terms and phrases
        $key_terms = $this->extract_key_terms_and_phrases($content);
        if (!empty($key_terms)) {
            $takeaways['important_terms'] = array_keys(array_slice($key_terms, 0, 7)); // Reduce to 7 terms
        }
        
        return $takeaways;
    }
    
    /**
     * Generate a simple summary from content
     *
     * @param string $content The content to summarize
     * @return string Summary text
     */
    public function generate_summary($content) {
        $sentence_count = $this->options['summary_sentence_count'] ?? 3;

        // Split content into sentences
        $sentences = preg_split('/[.!?]+/', strip_tags($content));
        
        // Score sentences based on position and keywords
        $scored_sentences = array();
        $important_words = array_map('trim', explode(',', $this->options['summary_important_words'] ?? ''));
        $important_words = array_filter($important_words); // Remove empty strings
        if (empty($important_words)) {
            $important_words = array(
                // Articles
                'the', 'a', 'an',
                
                // Prepositions
                'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 
                'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'throughout', 'inside',
                'outside', 'upon', 'under', 'over', 'within', 'without', 'across', 'along', 'beside', 'beyond',
                'near', 'past', 'since', 'until', 'unto', 'onto', 'toward', 'towards',
                
                // Pronouns
                'i', 'me', 'my', 'mine', 'myself', 'we', 'us', 'our', 'ours', 'ourselves', 'you', 'your', 'yours', 
                'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 
                'itself', 'they', 'them', 'their', 'theirs', 'themselves',
                
                // Demonstratives
                'this', 'that', 'these', 'those',
                
                // Auxiliary verbs
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 
                'done', 'doing', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'shall',
                
                // Adverbs
                'so', 'very', 'just', 'now', 'then', 'here', 'there', 'where', 'when', 'why', 'how', 'not', 'also', 
                'too', 'only', 'even', 'still', 'already', 'yet', 'never', 'always', 'often', 'sometimes', 'usually',
                'seldom', 'rarely', 'ever', 'nevertheless', 'nonetheless', 'however', 'moreover', 'furthermore',
                'otherwise', 'indeed', 'rather', 'quite', 'really', 'actually', 'basically', 'clearly', 'definitely',
                'especially', 'exactly', 'extremely', 'fairly', 'finally', 'further', 'generally', 'hardly', 'largely',
                'mainly', 'merely', 'nearly', 'newly', 'normally', 'obviously', 'particularly', 'possibly', 'primarily',
                'probably', 'recently', 'seriously', 'significantly', 'similarly', 'slightly', 'specifically', 'strongly',
                'subsequently', 'successfully', 'suddenly', 'supposedly', 'terribly', 'totally', 'truly', 'typically',
                'ultimately', 'unfortunately', 'virtually', 'apparently', 'certainly', 'completely', 'constantly',
                'directly', 'effectively', 'entirely', 'essentially', 'eventually', 'exceedingly', 'fully', 'greatly',
                'highly', 'immediately', 'increasingly', 'initially', 'literally', 'marked', 'necessarily', 'nicely',
                'ordinarily', 'perfectly', 'precisely', 'properly', 'purely', 'readily', 'regularly', 'relatively',
                'roughly', 'seemingly', 'significantly', 'simply', 'sincerely', 'slightly', 'steadily', 'strictly',
                'strongly', 'substantially', 'suddenly', 'surely', 'traditionally', 'unexpectedly', 'unusually',
                'utterly', 'vaguely', 'virtually', 'widely',
                
                // Conjunctions
                'if', 'as', 'than', 'while', 'because', 'since', 'unless', 'although', 'though', 'whereas', 'whether',
                'after', 'before', 'once', 'until', 'when', 'whenever', 'where', 'wherever', 'while',
                
                // Interjections and common words
                'oh', 'ah', 'hey', 'hi', 'hello', 'okay', 'well', 'yes', 'no', 'please', 'thanks', 'thank', 'welcome',
                
                // Other common words
                'get', 'got', 'getting', 'make', 'making', 'made', 'take', 'taking', 'took', 'taken', 'go', 'going', 'went',
                'gone', 'come', 'coming', 'came', 'see', 'seeing', 'saw', 'seen', 'know', 'knowing', 'knew', 'known',
                'think', 'thinking', 'thought', 'say', 'saying', 'said', 'tell', 'telling', 'told', 'give', 'giving',
                'gave', 'given', 'find', 'finding', 'found', 'put', 'try', 'trying', 'tried', 'use', 'using', 'used',
                'mean', 'meaning', 'meant', 'keep', 'keeping', 'kept', 'let', 'lets', 'letting', 'begin', 'beginning',
                'began', 'begun', 'seem', 'seeming', 'seemed', 'become', 'becoming', 'became', 'become', 'turn', 'turning', 'turned',
                'start', 'starting', 'started', 'run', 'running', 'ran', 'several', 'many', 'much', 'few', 'little',
                'lot', 'lots', 'plenty', 'enough', 'whole', 'half', 'quarter', 'double', 'triple', 'first', 'second',
                'third', 'last', 'next', 'previous', 'another', 'other', 'same', 'different', 'similar', 'such',
                'every', 'each', 'all', 'any', 'some', 'no', 'none', 'nothing', 'something', 'anything', 'everything',
                'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven', 'twelve',
                'twenty', 'thirty', 'forty', 'fifty', 'hundred', 'thousand', 'million', 'billion', 'both', 'either',
                'neither', 'least', 'less', 'more', 'most', 'better', 'best', 'worse', 'worst', 'rather', 'quite',
                'fairly', 'pretty', 'really', 'very', 'too', 'enough', 'so', 'as', 'how', 'what', 'whatever', 'which',
                'whichever', 'who', 'whoever', 'whom', 'whose', 'why', 'where', 'wherever', 'when', 'whenever'
            );
        }
        
        // Return empty string if no sentences
        if (empty($sentences)) {
            return '';
        }
        
        // Simple approach: return first few sentences
        $summary_sentences = array_slice($sentences, 0, $sentence_count);
        return implode('. ', $summary_sentences) . '.';
    }
    
    /**
     * Extract headings from content
     *
     * @param string $content The content to analyze
     * @return array Array of headings
     */
    private function extract_from_headings($content) {
        $headings = array();
        preg_match_all('/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i', $content, $matches);
        if (!empty($matches[1])) {
            $headings = array_map('strip_tags', $matches[1]);
            $headings = array_map('trim', $headings);
            $headings = array_filter($headings);
        }
        return $headings;
    }
    
    /**
     * Extract list items from content
     *
     * @param string $content The content to analyze
     * @return array Array of list items
     */
    private function extract_list_items($content) {
        $list_items = array();
        preg_match_all('/<li[^>]*>(.*?)<\/li>/i', $content, $matches);
        if (!empty($matches[1])) {
            $list_items = array_map('strip_tags', $matches[1]);
            $list_items = array_map('trim', $list_items);
            $list_items = array_filter($list_items);
        }
        return $list_items;
    }
    
    /**
     * Extract key terms and phrases from content
     *
     * @param string $content The content to analyze
     * @return array Array of key terms with their frequency
     */
    private function extract_key_terms_and_phrases($content) {
        // Remove HTML tags
        $text = strip_tags($content);
        
        // Convert to lowercase
        $text = strtolower($text);
        
        // Split into words
        $words = preg_split('/\s+/', $text);
        
        // Remove stop words
        $stop_words = array_map('trim', explode(',', $this->options['summary_stop_words'] ?? ''));
        $stop_words = array_filter($stop_words); // Remove empty strings
        
        if (empty($stop_words)) {
            $stop_words = array(
                'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
                'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
                'to', 'was', 'will', 'with', 'i', 'you', 'we', 'they', 'she', 'he',
                'them', 'their', 'this', 'these', 'those', 'but', 'or', 'not', 'have',
                'had', 'do', 'does', 'did', 'can', 'could', 'should', 'would', 'may',
                'might', 'must', 'shall', 'will', 'am', 'been', 'being', 'my', 'your',
                'our', 'her', 'his', 'its', 'me', 'him', 'us', 'them'
            );
        }
        
        // Filter out stop words
        $filtered_words = array_diff($words, $stop_words);
        
        // Count frequency of each word
        $word_count = array_count_values($filtered_words);
        
        // Sort by frequency (highest first)
        arsort($word_count);
        
        return $word_count;
    }

    /**
     * Format takeaways as HTML for display
     *
     * @param array $takeaways Array of takeaways from generate_key_takeaways()
     * @return string HTML formatted takeaways
     */
    public function format_takeaways_html($takeaways) {
        if (empty($takeaways)) {
            return '';
        }

        $html = '<div class="aege-summary">';

        // Add key sections if available
        if (!empty($takeaways['key_sections'])) {
            $html .= '<div class="aege-key-sections">';
            $html .= '<h2>Key Sections</h2>';
            $html .= '<ul>';
            foreach ($takeaways['key_sections'] as $section) {
                $html .= '<li>' . esc_html($section) . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        // Add key points if available
        if (!empty($takeaways['key_points'])) {
            $html .= '<div class="aege-key-points">';
            $html .= '<h2>Key Points</h2>';
            $html .= '<ul>';
            foreach ($takeaways['key_points'] as $point) {
                $html .= '<li>' . esc_html($point) . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        // Add important terms if available
        if (!empty($takeaways['important_terms'])) {
            $html .= '<div class="aege-important-terms">';
            $html .= '<h2>Important Terms</h2>';
            $html .= '<div class="aege-terms-list">';
            foreach ($takeaways['important_terms'] as $term) {
                $html .= '<span class="aege-term">' . esc_html($term) . '</span> ';
            }
            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}