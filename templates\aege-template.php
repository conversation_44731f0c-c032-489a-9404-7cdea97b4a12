<?php
/**
 * The template for displaying AEGE-optimized content.
 *
 * This template is designed for maximum machine-readability.
 * It is intentionally simple and devoid of styling.
 *
 * @package AEGE
 * @version 1.1.1
 */

// We should have these variables passed from the generator class.
// Add checks to ensure they exist to prevent errors.
if ( ! isset( $aege_post, $aege_content, $aege_schema ) ) {
    // If the variables aren't set, something went wrong.
    // We can output an HTML comment for debugging.
    echo '<!-- AEGE Template Error: Required variables not found. -->';
    return;
}
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
<title>AEGE Version of: <?php echo esc_html( get_the_title( $aege_post->ID ) ); ?></title>
    
    <?php
    /**
     * AEGE pages should generally not be indexed to avoid duplicate content issues.
     * The canonical link points to the original page which should be indexed instead.
     */
    ?>
    <meta name="robots" content="noindex, follow" />
    
    <?php
    /**
     * CRUCIAL: The canonical link MUST point back to the original user-facing page.
     * This tells search engines that this is an alternate version, not duplicate content.
     */
    ?>
    <link rel="canonical" href="<?php echo esc_url( get_permalink( $aege_post->ID ) ); ?>" />

    <?php
    /**
     * The generated Schema.org JSON-LD is injected directly into the head.
     * This is the most important part for machine understanding.
     */
    ?>
    <?php
    // Debug: Show schema type
    if (isset($aege_schema['@graph'])) {
        echo "<!-- AEGE Debug: Using graph schema with " . count($aege_schema['@graph']) . " schemas -->";
    } else {
        echo "<!-- AEGE Debug: Using simple schema -->";
    }
    ?>
    <script type="application/ld+json">
        <?php 
        // Handle both graph and simple schema formats
        echo wp_json_encode($aege_schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        ?>
    </script>
</head>
<body>

    <main>
        <article>
            <header>
                <h1><?php echo esc_html( get_the_title( $aege_post->ID ) ); ?></h1>
            </header>
            
            <div class="aege-content">
                <?php
                /** 
                 * Output key takeaways/summary if available
                 */
                if (!empty($aege_summary)) {
                    echo $aege_summary;
                }
                
                /**
                 * Output the pre-processed and sanitized content.
                 * We don't use the_content() here because all processing has already been done.
                 * The content is output as-is.
                 */
                // The content is already sanitized with wp_kses_post in the generator.
                // It's safe to echo directly.
                echo $aege_content;
                ?>
            </div>
        </article>
    </main>

</body>
</html>
