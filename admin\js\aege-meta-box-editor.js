// admin/js/aege-meta-box-editor.js

jQuery(document).ready(function($) {

    var editorId = 'aege_llm_content';
    // The static parent container for our meta box. This is crucial for the buttons.
    var metaBoxContainer = $('#aege_llm_meta_box'); 

    // --- PART 1: The "After Save" Fix ---
    // This is the most robust way to handle the editor refresh.
    $(document).on('wp-after-save-post', function() {
        var editorContainer = $('#aege_llm_content_editor_container');
        if (editorContainer.length) {
            editorContainer.html('<p style="text-align:center;">Loading editor...</p>');

            var data = {
                action: 'aege_refresh_llm_editor',
                security: $('#aege_llm_editor_nonce').val(),
                post_id: $('#post_ID').val()
            };

            // Call our new PHP endpoint to get a fresh editor
            $.post(ajaxurl, data, function(response) {
                editorContainer.html(response);
                // Let WordPress's own scripts initialize the new editor
                if (typeof wp.editor.initialize === 'function') {
                    wp.editor.initialize(editorId);
                }
            });
        }
    });

    // --- PART 2: The Button Fix (Event Delegation) ---
    // This makes sure your buttons work even after the editor is refreshed.

    // Listener for the "Import Content" button
    metaBoxContainer.on('click', '#aege-import-content', function() {
        var mainContent = '';
        if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
            mainContent = tinymce.get('content').getContent();
        } else if ($('#content').length) {
            mainContent = $('#content').val();
        }
        
        var llmEditor = tinymce.get(editorId);
        if (llmEditor) {
            llmEditor.setContent(mainContent);
        } else {
            $('#' + editorId).val(mainContent);
        }
    });

    // Listener for the "Detect Schemas" button
    metaBoxContainer.on('click', '#aege-detect-schemas', function() {
        alert('Detect Schemas Clicked!');
        // Your logic for this button goes here
    });

});