<?php
/**
 * Manages the caching of generated AEGE pages using both WordPress Transients API and Database caching.
 *
 * @package AEGE
 * @version 1.1.1
 */
class AEGE_Cache_Manager {

    /**
     * Initialize the cache manager
     */
    public static function init() {
        // Autoptimize
        if (defined('AUTOPTIMIZE_PLUGIN_DIR')) {
            add_filter('autoptimize_filter_html_before_minify', array('AEGE_Cache_Manager', 'exclude_from_autoptimize'));
        }

        // WP Rocket
        if (defined('WP_ROCKET_VERSION')) {
            add_filter('rocket_cache_reject_uri', array('AEGE_Cache_Manager', 'exclude_from_wp_rocket'));
        }

        // W3 Total Cache
        if (defined('W3TC')) {
            add_filter('w3tc_pagecache_reject_uri', array('AEGE_Cache_Manager', 'exclude_from_w3_total_cache'));
        }

        // WP Super Cache
        if (defined('WPSUPERCACHE')) {
            add_filter('wp_cache_reject_uri', array('AEGE_Cache_Manager', 'exclude_from_wp_super_cache'));
        }

        // LiteSpeed Cache
        if (defined('LSCWP_V')) {
            add_filter('litespeed_cache_exclude_path', array('AEGE_Cache_Manager', 'exclude_from_litespeed'));
        }
        
        // Schedule cache cleanup
        if (!wp_next_scheduled('aege_cleanup_expired_cache')) {
            wp_schedule_event(time(), 'daily', 'aege_cleanup_expired_cache');
        }
        add_action('aege_cleanup_expired_cache', array('AEGE_Cache_Manager', 'cleanup_expired_cache'));
    }

    /**
     * Retrieves the cached HTML for a specific post.
     *
     * @param int $object_id The ID of the object to retrieve from cache.
     * @param string $object_type The type of the object (e.g., 'post', 'term').
     * @return string|false The cached HTML content, or false if nothing is cached.
     */
    public static function get_cache($object_id, $object_type) {
        if (class_exists('AEGE_Database_Cache')) {
            AEGE_Logger::debug('Checking database cache', array(
                'object_id' => $object_id,
                'object_type' => $object_type
            ));
            
            $cached_data = AEGE_Database_Cache::get_cached_content($object_id, $object_type);
            if ($cached_data && !empty($cached_data['content'])) {
                AEGE_Logger::debug('Cache hit', array(
                    'object_id' => $object_id,
                    'object_type' => $object_type
                ));
                return $cached_data['content'];
            } else {
                AEGE_Logger::debug('Cache miss', array(
                    'object_id' => $object_id,
                    'object_type' => $object_type
                ));
            }
        }
        return false;
    }

    /**
     * Saves the generated HTML for a specific object into the cache.
     *
     * @param int    $object_id      The ID of the object to cache.
     * @param string $object_type    The type of the object (e.g., 'post', 'term').
     * @param string $html_content The HTML content to be cached.
     */
    public static function set_cache($object_id, $object_type, $html_content) {
        $cache_key = 'aege_' . AEGE_VERSION . '_' . $object_type . '_' . $object_id;
        AEGE_Logger::debug('Setting cache', array(
            'object_id' => $object_id,
            'object_type' => $object_type,
            'cache_key' => $cache_key,
            'content_length' => strlen($html_content)
        ));
        
        if (class_exists('AEGE_Database_Cache')) {
            $data = array(
                'content' => $html_content,
            );
            // Add more metadata if available for specific object types
            if ($object_type === 'post') {
                $post = get_post($object_id);
                if ($post) {
                    $data['title'] = get_the_title($object_id);
                    $data['link'] = get_permalink($object_id);
                    $data['published'] = $post->post_date_gmt;
                    $data['modified'] = $post->post_modified_gmt;
                }
            } elseif (strpos($object_type, 'term_') === 0) {
                $taxonomy = str_replace('term_', '', $object_type);
                $term = get_term($object_id, $taxonomy);
                if ($term && !is_wp_error($term)) {
                    $data['title'] = $term->name;
                    $data['link'] = get_term_link($term);
                    $data['published'] = null; // Terms don't have published dates directly
                    $data['modified'] = null; // Terms don't have modified dates directly
                }
            }
            AEGE_Database_Cache::save_to_cache($object_id, $object_type, $data, $cache_key);
        }
    }

    /**
     * Deletes the cache for a specific object.
     *
     * @param int $object_id The ID of the object whose cache should be cleared.
     * @param string $object_type The type of the object (e.g., 'post', 'term').
     */
    public static function clear_cache_for_object($object_id, $object_type) {
        if (class_exists('AEGE_Database_Cache')) {
            AEGE_Database_Cache::clear_cache_for_object($object_id, $object_type);
        }
        
        // Clear page caches for this object
        if ($object_type === 'post') {
            self::clear_page_cache_for_post($object_id);
        } elseif (strpos($object_type, 'term_') === 0) {
            $taxonomy = str_replace('term_', '', $object_type);
            self::clear_page_cache_for_term($object_id, $taxonomy);
        }
        
        // Trigger action for cache clearing
        do_action('aege_object_cache_cleared', $object_id, $object_type);
    }

    /**
     * Clears all AEGE caches.
     */
    public static function clear_all_cache() {
        if (class_exists('AEGE_Database_Cache')) {
            AEGE_Database_Cache::clear_all_cache();
        }
        
        // Clear all page caches
        self::clear_all_page_caches();
        
        // Trigger action for cache clearing
        do_action('aege_cache_cleared');
    }

    /**
     * Clear page caches for a specific post
     */
    public static function clear_page_cache_for_post($post_id) {
        // Get the AEGE URL for this post
        $aege_url = trailingslashit(get_permalink($post_id)) . 'llm/';
        
        // Clear various page caches
        self::clear_wp_rocket_page_cache($aege_url);
        self::clear_litespeed_page_cache($aege_url);
    }

    /**
     * Clear page caches for a specific term
     */
    public static function clear_page_cache_for_term($term_id, $taxonomy) {
        $aege_url = trailingslashit(get_term_link($term_id, $taxonomy)) . 'llm/';
        
        // Clear various page caches
        self::clear_wp_rocket_page_cache($aege_url);
        self::clear_litespeed_page_cache($aege_url);
        // Add other caching plugins here
    }

    /**
     * Clear all page caches from supported caching plugins
     */
    public static function clear_all_page_caches() {
        self::clear_autoptimize_cache();
        self::clear_wp_rocket_cache();
        self::clear_w3_total_cache();
        self::clear_wp_super_cache();
        self::clear_litespeed_cache();
        self::clear_wp_fastest_cache();
    }

    /**
     * Clear Autoptimize cache
     */
    public static function clear_autoptimize_cache() {
        if (class_exists('autoptimizeCache')) {
            autoptimizeCache::clearall();
        }
    }

    /**
     * Clear WP Rocket cache
     */
    public static function clear_wp_rocket_cache() {
        if (function_exists('rocket_clean_domain')) {
            rocket_clean_domain();
        }
    }

    /**
     * Clear W3 Total Cache
     */
    public static function clear_w3_total_cache() {
        if (function_exists('w3tc_pgcache_flush')) {
            w3tc_pgcache_flush();
        }
    }

    /**
     * Clear WP Super Cache
     */
    public static function clear_wp_super_cache() {
        if (function_exists('wp_cache_clear_cache')) {
            wp_cache_clear_cache();
        }
    }

    /**
     * Clear LiteSpeed Cache
     */
    public static function clear_litespeed_cache() {
        if (class_exists('LiteSpeed\Purge')) {
            \LiteSpeed\Purge::purge_all();
        }
    }

    /**
     * Clear WP Fastest Cache
     */
    public static function clear_wp_fastest_cache() {
        if (class_exists('WpFastestCache')) {
            if (method_exists('WpFastestCache', 'deleteCache')) {
                $wpfc = new WpFastestCache();
                $wpfc->deleteCache(true);
            }
        }
    }

    /**
     * Clear WP Rocket cache for specific URL
     */
    public static function clear_wp_rocket_page_cache($url) {
        if (function_exists('rocket_clean_files')) {
            rocket_clean_files($url);
        }
    }

    /**
     * Clear LiteSpeed cache for specific URL
     */
    public static function clear_litespeed_page_cache($url) {
        if (class_exists('LiteSpeed\Purge')) {
            \LiteSpeed\Purge::purge_url($url);
        }
    }

    /**
     * Exclude AEGE endpoints from Autoptimize
     */
    public static function exclude_from_autoptimize($exclude) {
        if (is_string($exclude) && isset($_SERVER['REQUEST_URI'])) {
            $request_uri = sanitize_text_field(wp_unslash($_SERVER['REQUEST_URI']));
            if (strpos($request_uri, 'llm') !== false) {
                return true;
            }
        }
        return $exclude;
    }

    /**
     * Exclude AEGE endpoints from WP Rocket
     */
    public static function exclude_from_wp_rocket($excluded) {
        $excluded[] = '/(.*)llm(.*)';
        $excluded[] = '/aege-sitemap\.xml';
        return $excluded;
    }

    /**
     * Exclude AEGE endpoints from W3 Total Cache
     */
    public static function exclude_from_w3_total_cache($excluded) {
        $excluded[] = 'llm(.*)';
        $excluded[] = 'aege-sitemap\.xml';
        return $excluded;
    }

    /**
     * Exclude AEGE endpoints from WP Super Cache
     */
    public static function exclude_from_wp_super_cache($excluded) {
        $excluded[] = 'llm(.*)';
        $excluded[] = 'aege-sitemap\.xml';
        return $excluded;
    }

    /**
     * Exclude AEGE endpoints from LiteSpeed Cache
     */
    public static function exclude_from_litespeed($excluded) {
        $excluded[] = '/(.*)llm(.*)';
        $excluded[] = '/aege-sitemap\.xml';
        return $excluded;
    }
    
    /**
     * Cleanup expired cache entries
     */
    public static function cleanup_expired_cache() {
        if (class_exists('AEGE_Database_Cache')) {
            AEGE_Database_Cache::delete_expired_cache();
            AEGE_Database_Cache::optimize_table();
        }
    }
}
