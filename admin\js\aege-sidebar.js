// admin/js/aege-sidebar.js

// Import the necessary components from WordPress's own script libraries.
const { registerPlugin } = wp.plugins;
const { PluginSidebar, PluginSidebarMoreMenuItem } = wp.editPost;
const { PanelBody } = wp.components;
const el = wp.element.createElement;
const __ = wp.i18n.__;

// The name of our plugin sidebar icon (you can choose any from the Dashicons list).
const icon = 'superhero-alt';
const title = __('AEGE Optimizer', 'aege');

// This is the main component for our sidebar.
const AegePluginSidebar = () => (
    el( PluginSidebarMoreMenuItem,
        {
            target: 'aege-sidebar', // A unique name for our sidebar panel
            icon: icon
        },
        title
    ),
    el( PluginSidebar,
        {
            name: 'aege-sidebar',
            icon: icon,
            title: title
        },
        el( PanelBody,
            {},
            // This is where your meta box content will eventually go.
            // For now, we are just creating the panel. The meta box will appear below the main editor.
            __('AEGE settings and tools will be displayed here in a future update.', 'aege')
        )
    )
);

// Register the plugin with WordPress.
registerPlugin('aege-sidebar', {
    render: AegePluginSidebar
});