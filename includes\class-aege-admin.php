<?php
class AEGE_Admin {

    private $plugin_name;
    private $version;
    private $options;
    // A private static property to hold the single instance of the class
    private static $instance = null;

    // Make the constructor private to prevent direct creation
    private function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->options = get_option('aege_settings');
    }

    // The public static method to get the instance
    public static function get_instance($plugin_name, $version) {
        if (null === self::$instance) {
            self::$instance = new self($plugin_name, $version);
        }
        return self::$instance;
    }
    
    // The main run method where all hooks are registered
    public function run() {
        // Enqueue admin scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Add action for background scorecard update
        add_action('aege_update_scorecard_data_hook', [$this, 'update_scorecard_data_in_background']);
        
        // Hooks for adding and rendering custom columns for Posts and Pages
        add_filter('manage_posts_columns', [$this, 'add_aege_columns']);
        add_action('manage_posts_custom_column', [$this, 'render_aege_columns'], 10, 2);
        add_filter('manage_pages_columns', [$this, 'add_aege_columns']);
        add_action('manage_pages_custom_column', [$this, 'render_aege_columns'], 10, 2);
        
        // Hooks for making the new columns sortable
        add_filter('manage_edit-post_sortable_columns', [$this, 'make_aege_columns_sortable']);
        add_filter('manage_edit-page_sortable_columns', [$this, 'make_aege_columns_sortable']);
        add_action('pre_get_posts', [$this, 'handle_aege_column_sorting']);
        
        // Hooks for adding column styles
        add_action('admin_head', [$this, 'add_aege_column_styles']);
        
    // Add metabox
    add_action('add_meta_boxes', array($this, 'add_llm_meta_box'));
    
    // Handle saving the metabox data
    add_action('save_post', array($this, 'save_llm_meta_box_data'));
    
    // Add AJAX endpoint for refreshing LLM editor
    add_action('wp_ajax_aege_refresh_llm_editor', [$this, 'ajax_refresh_llm_editor_callback']);
    add_action('wp_ajax_aege_save_llm_content', array($this, 'ajax_save_llm_content'));
        
        // Register the settings themselves
        add_action('admin_init', array($this, 'register_settings'));
        
        // Add the main settings page
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Add AJAX endpoint for refreshing LLM editor
        add_action('wp_ajax_aege_refresh_llm_editor', [$this, 'ajax_refresh_llm_editor_callback']);
        add_action('wp_ajax_aege_save_llm_content', array($this, 'ajax_save_llm_content'));
    }

    public function add_admin_menu() {
        add_menu_page(
            __( 'AEGE Content Optimizer Settings', 'aege' ),
            __( 'AEGE Optimizer', 'aege' ),
            'manage_options',
            $this->plugin_name,
            array( $this, 'create_admin_page' ),
            'dashicons-superhero-alt', // Icon
            26
        );
        
        // Add sub-menu pages
        add_submenu_page(
            $this->plugin_name,
            __( 'AEGE Dashboard', 'aege' ),
            __( 'Dashboard', 'aege' ),
            'manage_options',
            $this->plugin_name,
            array( $this, 'create_admin_page' )
        );
        
        add_submenu_page(
            $this->plugin_name,
            __( 'AEGE Content Analysis', 'aege' ),
            __( 'Content Analysis', 'aege' ),
            'manage_options',
            $this->plugin_name . '-analysis',
            array( $this, 'create_analysis_page' )
        );

        add_submenu_page(
            $this->plugin_name,
            __( 'AEGE Status', 'aege' ),
            __( 'Status', 'aege' ),
            'manage_options',
            $this->plugin_name . '-status',
            array( $this, 'create_status_page' )
        );
    }

    public function create_status_page() {
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'AEGE System Status', 'aege' ); ?></h1>
            <p><?php esc_html_e( 'This page runs a series of checks to ensure the plugin is configured correctly.', 'aege' ); ?></p>
            <table class="wp-list-table widefat striped">
                <thead>
                    <tr>
                        <th style="width: 50px;"><?php esc_html_e( 'Status', 'aege' ); ?></th>
                        <th><?php esc_html_e( 'Check', 'aege' ); ?></th>
                        <th><?php esc_html_e( 'Recommendation', 'aege' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $health_checks = $this->run_health_checks();
                    foreach ($health_checks as $check) {
                        $status_icon = $check['status'] === 'pass' ? '<span class="dashicons dashicons-yes-alt" style="color: #4CAF50;"></span>' : '<span class="dashicons dashicons-warning" style="color: #f44336;"></span>';
                        ?>
                        <tr>
                            <td><?php echo $status_icon; ?></td>
                            <td><strong><?php echo esc_html($check['label']); ?></strong><br><?php echo esc_html($check['message']); ?></td>
                            <td><?php echo wp_kses_post($check['recommendation']); ?></td>
                        </tr>
                        <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        <?php
    }

    private function run_health_checks() {
        $checks = array();
        $checks[] = $this->check_master_switch();
        $checks[] = $this->check_llms_txt_presence();
        $checks[] = $this->check_robots_txt_configuration();
        $checks[] = $this->check_database_cache_table();
        $checks[] = $this->check_wordpress_rest_api();
        $checks[] = $this->check_aege_rest_api_setting();
        $checks[] = $this->check_key_takeaways_setting();
        $checks[] = $this->check_workflow_setting();
        return $checks;
    }

    private function check_master_switch() {
        $options = get_option('aege_settings');
        if (!empty($options['master_switch'])) {
            return array(
                'label' => 'Plugin Master Switch',
                'status' => 'pass',
                'message' => 'The plugin master switch is on.',
                'recommendation' => 'No action needed.'
            );
        } else {
            return array(
                'label' => 'Plugin Master Switch',
                'status' => 'fail',
                'message' => 'The plugin master switch is off.',
                'recommendation' => 'Please enable the master switch in the <a href="' . admin_url('admin.php?page=aege') . '">AEGE settings</a> to enable plugin functionality.'
            );
        }
    }

    private function check_llms_txt_presence() {
        $file_path = get_home_path() . 'llms.txt';
        if (file_exists($file_path)) {
            return array(
                'label' => 'llms.txt File Presence',
                'status' => 'pass',
                'message' => 'The llms.txt file was found in your WordPress root directory.',
                'recommendation' => 'No action needed.'
            );
        } else {
            return array(
                'label' => 'llms.txt File Presence',
                'status' => 'fail',
                'message' => 'The llms.txt file was not found in your WordPress root directory.',
                'recommendation' => 'Please go to the <a href="' . admin_url('admin.php?page=aege') . '">AEGE settings</a> and click the "Create llms.txt File" button.'
            );
        }
    }

    private function check_robots_txt_configuration() {
        $file_path = get_home_path() . 'robots.txt';
        if (file_exists($file_path)) {
            $robots_content = file_get_contents($file_path);
            if (preg_match('/^LLMs:.*\\/llms\\.txt/m', $robots_content)) {
                return array(
                    'label' => 'robots.txt Configuration',
                    'status' => 'pass',
                    'message' => 'Your robots.txt file correctly references llms.txt.',
                    'recommendation' => 'No action needed.'
                );
            } else {
                return array(
                    'label' => 'robots.txt Configuration',
                    'status' => 'fail',
                    'message' => 'Your robots.txt file does not seem to reference llms.txt.',
                    'recommendation' => 'For best results, add the following line to your robots.txt file: <code>LLMs: ' . home_url('/llms.txt') . '</code>'
                );
            }
        } else {
            return array(
                'label' => 'robots.txt Configuration',
                'status' => 'fail',
                'message' => 'A robots.txt file was not found in your WordPress root directory.',
                'recommendation' => 'Please create a robots.txt file in your WordPress root directory and add the following line: <code>LLMs: ' . home_url('/llms.txt') . '</code>'
            );
        }
    }

    private function check_database_cache_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'aege_cache';
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
            return array(
                'label' => 'Database Cache',
                'status' => 'pass',
                'message' => 'The custom database cache table exists.',
                'recommendation' => 'No action needed.'
            );
        } else {
            return array(
                'label' => 'Database Cache',
                'status' => 'fail',
                'message' => 'The custom database cache table does not exist.',
                'recommendation' => 'Please deactivate and reactivate the AEGE plugin to trigger the table creation.'
            );
        }
    }

    /**
     * Check WordPress REST API status
     */
    private function check_wordpress_rest_api() {
        if (get_option('permalink_structure')) {
            return array(
                'label' => 'WordPress REST API',
                'status' => 'pass',
                'message' => 'The WordPress REST API is enabled.',
                'recommendation' => 'No action needed.'
            );
        } else {
            return array(
                'label' => 'WordPress REST API',
                'status' => 'fail',
                'message' => 'The WordPress REST API is disabled because pretty permalinks are not enabled.',
                'recommendation' => 'Please enable pretty permalinks in your <a href="' . admin_url('options-permalink.php') . '">Permalink Settings</a>.'
            );
        }
    }
    
    /**
     * Check AEGE REST API setting
     */
    private function check_aege_rest_api_setting() {
        $options = get_option('aege_settings');
        if (isset($options['enable_rest_api']) && $options['enable_rest_api'] == 1) {
            return array(
                'label' => 'AEGE REST API Setting',
                'status' => 'pass',
                'message' => 'The AEGE REST API is enabled in settings.',
                'recommendation' => 'No action needed.'
            );
        } else {
            return array(
                'label' => 'AEGE REST API Setting',
                'status' => 'info',
                'message' => 'The AEGE REST API is disabled in settings (secure default).',
                'recommendation' => 'Enable the REST API in <a href="' . admin_url('admin.php?page=aege') . '">AEGE Settings</a> under API Settings only if you plan to use external LLM integrations.'
            );
        }
    }
    
    /**
     * Check Key Takeaways setting
     */
    private function check_key_takeaways_setting() {
        $options = get_option('aege_settings');
        if (isset($options['enable_summaries']) && $options['enable_summaries'] == 1) {
            return array(
                'label' => 'Key Takeaways Feature',
                'status' => 'pass',
                'message' => 'Key takeaways/summaries are enabled for AEGE content.',
                'recommendation' => 'No action needed.'
            );
        } else {
            return array(
                'label' => 'Key Takeaways Feature',
                'status' => 'info',
                'message' => 'Key takeaways/summaries are disabled.',
                'recommendation' => 'Enable key takeaways in <a href="' . admin_url('admin.php?page=aege') . '">AEGE Settings</a> under Content & Automation if you want AI-friendly summaries.'
            );
        }
    }
    
    }

    public function create_admin_page() {
        // Handle cache clearing actions
        $this->handle_cache_clearing_actions();
        
        // Handle log clearing (not viewing)
        if (isset($_GET['aege_action']) && $_GET['aege_action'] === 'clear_logs' && current_user_can('manage_options')) {
            AEGE_Logger::clear_log();
            add_settings_error('aege_messages', 'aege_logs_cleared', 'Logs cleared successfully.', 'updated');
        }
        
        // Handle rewrite rules flushing
        if (isset($_GET['aege_action']) && $_GET['aege_action'] === 'flush_rewrite_rules' && current_user_can('manage_options')) {
            flush_rewrite_rules();
            add_settings_error('aege_messages', 'aege_rewrite_rules_flushed', 'Rewrite rules flushed successfully.', 'updated');
        }
        
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'AEGE Optimization for AI', 'aege' ); ?></h1>
            <p><?php esc_html_e( 'Configure how AEGE generates optimized content for Answer Engines and Generative Engines.', 'aege' ); ?></p>
            
            <?php if (current_user_can('manage_options')):
            ?><p><a href="<?php echo admin_url('admin.php?page=aege&aege_action=clear_cache'); ?>" class="button button-secondary"><?php esc_html_e( 'Clear AEGE Cache', 'aege' ); ?></a> 
            <a href="<?php echo admin_url('admin.php?page=aege&aege_action=clear_page_cache'); ?>" class="button button-secondary"><?php esc_html_e( 'Clear All Page Caches', 'aege' ); ?></a>
            <a href="<?php echo admin_url('admin.php?page=aege&aege_action=flush_rewrite_rules'); ?>" class="button button-secondary"><?php esc_html_e( 'Flush Rewrite Rules', 'aege' ); ?></a></p>
            <?php endif; ?>

            <?php settings_errors(); ?>

            <form method="post" action="options.php">
                <?php
                    settings_fields( 'aege_option_group' );
                    do_settings_sections( 'aege-admin' );
                    submit_button();
                ?>
            </form>
            
            <?php $this->render_api_documentation(); ?>
        </div>
        
        <?php
        // Note: Script enqueuing is now handled by enqueue_admin_scripts() method
        // to avoid conflicts and ensure proper localization
    }
    
    public function create_analysis_page() {
        $stats = $this->get_cached_aege_statistics();
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'AEGE Optimization Scorecard', 'aege' ); ?></h1>
            <p><?php esc_html_e( 'Track your Answer Engine Optimization (AEO) and Generative Engine Optimization (GEO) progress.', 'aege' ); ?></p>
            
            <!-- Bulk Update Button -->
            <div class="notice notice-info">
                <p><?php esc_html_e( 'If your statistics show zeros, click the button below to calculate metadata for existing AEGE content:', 'aege' ); ?></p>
                <p>
                    <button type="button" id="aege-bulk-update-btn" class="button button-primary"><?php esc_html_e( 'Update AEGE Statistics', 'aege' ); ?></button> 
                    <span id="aege-bulk-update-status"></span>
                </p>
                <div id="aege-bulk-update-progress" style="display: none; margin-top: 10px;">
                    <progress id="aege-bulk-update-progress-bar" value="0" max="100" style="width: 100%;"></progress>
                    <p id="aege-bulk-update-progress-text" style="text-align: center; margin: 5px 0;"></p>
                </div>
            </div>
            
            <!-- Existing Stats Cards -->
            <div class="aege-dashboard-stats">
                <div class="aege-stat-card">
                    <h3><?php esc_html_e( 'Total AEGE Pages', 'aege' ); ?></h3>
                    <p class="aege-stat-number"><?php echo $stats['aege_enabled']; ?></p>
                    <p class="aege-stat-description"><?php esc_html_e( 'Optimized for AEGE', 'aege' ); ?></p>
                </div>
                <!-- More existing cards -->
            </div>
            
            <!-- New Schema Coverage Section -->
            <div class="aege-dashboard-section">
                <h2><?php esc_html_e( 'Schema Coverage', 'aege' ); ?></h2>
                <p><?php esc_html_e( 'Structured data helps AI systems understand your content better.', 'aege' ); ?></p>
                <div class="aege-schema-stats">
                    <div class="aege-schema-item">
                        <span class="aege-schema-count"><?php echo $stats['schema_coverage']['faq']; ?></span>
                        <span class="aege-schema-type"><?php esc_html_e( 'FAQ Pages', 'aege' ); ?></span>
                    </div>
                    <div class="aege-schema-item">
                        <span class="aege-schema-count"><?php echo $stats['schema_coverage']['howto']; ?></span>
                        <span class="aege-schema-type"><?php esc_html_e( 'How-To Pages', 'aege' ); ?></span>
                    </div>
                    <div class="aege-schema-item">
                        <span class="aege-schema-count"><?php echo $stats['schema_coverage']['product']; ?></span>
                        <span class="aege-schema-type"><?php esc_html_e( 'Product Pages', 'aege' ); ?></span>
                    </div>
                    <div class="aege-schema-item">
                        <span class="aege-schema-count"><?php echo $stats['schema_coverage']['review']; ?></span>
                        <span class="aege-schema-type"><?php esc_html_e( 'Review Pages', 'aege' ); ?></span>
                    </div>
                    <div class="aege-schema-item">
                        <span class="aege-schema-count"><?php echo $stats['schema_coverage']['recipe']; ?></span>
                        <span class="aege-schema-type"><?php esc_html_e( 'Recipe Pages', 'aege' ); ?></span>
                    </div>
                    <div class="aege-schema-item">
                        <span class="aege-schema-count"><?php echo $stats['schema_coverage']['event']; ?></span>
                        <span class="aege-schema-type"><?php esc_html_e( 'Event Pages', 'aege' ); ?></span>
                    </div>
                </div>
                <div class="aege-chart-container">
                    <canvas id="schemaCoverageChart"></canvas>
                </div>
            </div>
            
            <!-- New Citation Density Section -->
            <div class="aege-dashboard-section">
                <h2><?php esc_html_e( 'Citation Density', 'aege' ); ?></h2>
                <p><?php esc_html_e( 'External sources add credibility and context to your content.', 'aege' ); ?></p>
                <div class="aege-citation-stats">
                    <div class="aege-citation-item">
                        <span class="aege-citation-count"><?php echo $stats['citation_density']['avg_citations_per_page']; ?></span>
                        <span class="aege-citation-type"><?php esc_html_e( 'Avg. Citations/Page', 'aege' ); ?></span>
                    </div>
                    <div class="aege-citation-item">
                        <span class="aege-citation-count"><?php echo $stats['citation_density']['pages_with_citations']; ?></span>
                        <span class="aege-citation-type"><?php esc_html_e( 'Pages with Citations', 'aege' ); ?></span>
                    </div>
                    <div class="aege-citation-item">
                        <span class="aege-citation-count"><?php echo $stats['citation_density']['total_citations']; ?></span>
                        <span class="aege-citation-type"><?php esc_html_e( 'Total Citations', 'aege' ); ?></span>
                    </div>
                </div>
                <div class="aege-chart-container">
                    <canvas id="citationDensityChart"></canvas>
                </div>
            </div>
            
            <!-- New Content Freshness Section -->
            <div class="aege-dashboard-section">
                <h2><?php esc_html_e( 'AEO Content Freshness', 'aege' ); ?></h2>
                <p><?php esc_html_e( 'Regularly updated AI-optimized content performs better in AI systems.', 'aege' ); ?></p>
                <div class="aege-freshness-stats">
                    <div class="aege-freshness-item">
                        <span class="aege-freshness-count"><?php echo $stats['content_freshness']['updated_30_days']; ?></span>
                        <span class="aege-freshness-type"><?php esc_html_e( 'Updated (30 days)', 'aege' ); ?></span>
                    </div>
                    <div class="aege-freshness-item">
                        <span class="aege-freshness-count"><?php echo $stats['content_freshness']['not_updated_1_year']; ?></span>
                        <span class="aege-freshness-type"><?php esc_html_e( 'Stale Content (>1 year)', 'aege' ); ?></span>
                    </div>
                </div>
                <div class="aege-chart-container">
                    <canvas id="freshnessTimelineChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- New Entity Density Section -->
        <div class="aege-dashboard-section">
            <h2><?php esc_html_e( 'Named Entity Density', 'aege' ); ?></h2>
            <p><?php esc_html_e( 'Named entities add semantic richness and context to your content.', 'aege' ); ?></p>
            <div class="aege-entity-stats">
                <div class="aege-entity-item">
                    <span class="aege-entity-count"><?php echo isset($stats['entity_analysis']) ? $stats['entity_analysis']['avg_entities_per_page'] : 0; ?></span>
                    <span class="aege-entity-type"><?php esc_html_e( 'Avg. Entities/Page', 'aege' ); ?></span>
                </div>
                <div class="aege-entity-item">
                    <span class="aege-entity-count"><?php echo isset($stats['entity_analysis']) ? $stats['entity_analysis']['pages_with_entities'] : 0; ?></span>
                    <span class="aege-entity-type"><?php esc_html_e( 'Pages with Entities', 'aege' ); ?></span>
                </div>
                <div class="aege-entity-item">
                    <span class="aege-entity-count"><?php echo isset($stats['entity_analysis']) ? $stats['entity_analysis']['total_entities'] : 0; ?></span>
                    <span class="aege-entity-type"><?php esc_html_e( 'Total Entities', 'aege' ); ?></span>
                </div>
            </div>
        </div>
        
        <!-- New Overall AEO Score Section -->
        <div class="aege-dashboard-section">
            <h2><?php esc_html_e( 'Overall AEO Score', 'aege' ); ?></h2>
            <p><?php esc_html_e( 'Your content\'s overall optimization for Answer Engines.', 'aege' ); ?></p>
            <div class="aege-score-stats">
                <div class="aege-score-item">
                    <span class="aege-score-count"><?php echo isset($stats['overall_aeo_score']) ? $stats['overall_aeo_score']['average_score'] : 0; ?></span>
                    <span class="aege-score-type"><?php esc_html_e( 'Average Score', 'aege' ); ?></span>
                </div>
                <div class="aege-score-item">
                    <span class="aege-score-count"><?php echo isset($stats['overall_aeo_score']) ? $stats['overall_aeo_score']['highest_score'] : 0; ?></span>
                    <span class="aege-score-type"><?php esc_html_e( 'Highest Score', 'aege' ); ?></span>
                </div>
                <div class="aege-score-item">
                    <span class="aege-score-count"><?php echo isset($stats['overall_aeo_score']) ? $stats['overall_aeo_score']['lowest_score'] : 0; ?></span>
                    <span class="aege-score-type"><?php esc_html_e( 'Lowest Score', 'aege' ); ?></span>
                </div>
            </div>
            <div class="aege-chart-container">
                <canvas id="aeoScoreChart"></canvas>
            </div>
        </div>
        <style>
        /* Enhanced dashboard styles */
        .aege-dashboard-section {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin: 20px 0;
            padding: 20px;
        }
        
        .aege-schema-stats, .aege-citation-stats, .aege-freshness-stats, .aege-entity-stats, .aege-score-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .aege-schema-item, .aege-citation-item, .aege-freshness-item, .aege-entity-item, .aege-score-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .aege-schema-count, .aege-citation-count, .aege-freshness-count, .aege-entity-count, .aege-score-count {
            display: block;
            font-size: 2em;
            font-weight: bold;
            color: #0073aa;
        }
        
        .aege-schema-type, .aege-citation-type, .aege-freshness-type, .aege-entity-type, .aege-score-type {
            display: block;
            color: #666;
            margin-top: 5px;
        }
        
        .aege-chart-container {
            height: 300px;
            margin-top: 20px;
        }
        </style>
        
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        // Initialize charts when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Schema coverage chart
            var schemaCtx = document.getElementById('schemaCoverageChart');
            if (schemaCtx) {
                schemaCtx = schemaCtx.getContext('2d');
                var schemaChart = new Chart(schemaCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['FAQ', 'How-To', 'Product', 'Review', 'Recipe', 'Event'],
                        datasets: [{
                            data: [
                                <?php echo $stats['schema_coverage']['faq'] ?? 0; ?>,
                                <?php echo $stats['schema_coverage']['howto'] ?? 0; ?>,
                                <?php echo $stats['schema_coverage']['product'] ?? 0; ?>,
                                <?php echo $stats['schema_coverage']['review'] ?? 0; ?>,
                                <?php echo $stats['schema_coverage']['recipe'] ?? 0; ?>,
                                <?php echo $stats['schema_coverage']['event'] ?? 0; ?>
                            ],
                            backgroundColor: [
                                '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#E91E63', '#00BCD4'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
            
            // Citation density chart
            var citationCtx = document.getElementById('citationDensityChart');
            if (citationCtx) {
                citationCtx = citationCtx.getContext('2d');
                var citationChart = new Chart(citationCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Avg. Citations/Page', 'Pages with Citations', 'Total Citations'],
                        datasets: [{
                            label: 'Citation Metrics',
                            data: [
                                <?php echo $stats['citation_density']['avg_citations_per_page'] ?? 0; ?>,
                                <?php echo $stats['citation_density']['pages_with_citations'] ?? 0; ?>,
                                <?php echo $stats['citation_density']['total_citations'] ?? 0; ?>
                            ],
                            backgroundColor: [
                                '#4CAF50', '#2196F3', '#FF9800'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // Freshness timeline chart
            var freshnessCtx = document.getElementById('freshnessTimelineChart');
            if (freshnessCtx) {
                freshnessCtx = freshnessCtx.getContext('2d');
                var freshnessChart = new Chart(freshnessCtx, {
                    type: 'line',
                    data: {
                        labels: <?php echo json_encode(array_keys($stats['content_freshness']['update_timeline'] ?? array())); ?>,
                        datasets: [{
                            label: 'Content Updates',
                            data: <?php echo json_encode(array_values($stats['content_freshness']['update_timeline'] ?? array())); ?>,
                            borderColor: '#0073aa',
                            backgroundColor: 'rgba(0,115, 170,0.1)',
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // AEO Score distribution chart
            var scoreCtx = document.getElementById('aeoScoreChart');
            if (scoreCtx) {
                scoreCtx = scoreCtx.getContext('2d');
                var scoreLabels = <?php echo isset($stats['overall_aeo_score']) ? json_encode(array_keys($stats['overall_aeo_score']['score_distribution'] ?? array())) : '[]'; ?>;
                var scoreData = <?php echo isset($stats['overall_aeo_score']) ? json_encode(array_values($stats['overall_aeo_score']['score_distribution'] ?? array())) : '[]'; ?>;
                
                var scoreChart = new Chart(scoreCtx, {
                    type: 'bar',
                    data: {
                        labels: scoreLabels,
                        datasets: [{
                            label: 'Number of Pages',
                            data: scoreData,
                            backgroundColor: '#0073aa'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        });
        </script>
        <?php
        // Render the content insights table
        $this->render_content_insights_table();
    }
    
    /**
     * Get enabled post types from settings
     *
     * @return array Array of enabled post types
     */
    private function get_enabled_post_types() {
        $options = get_option('aege_settings');
        return isset($options['post_types']) ? array_keys($options['post_types']) : array('post');
    }
    
    /**
     * Create a new public function to render the content insights table
     */
    public function render_content_insights_table() {
        // Get all AEGE-enabled posts. For performance, you might add pagination here later.
        $args = [
            'post_type' => $this->get_enabled_post_types(), // A helper function to get enabled post types
            'posts_per_page' => -1, // For now, get all. See note below.
            'meta_key' => '_aege_enabled',
            'meta_value' => '1',
        ];
        $aege_posts = get_posts($args);

        if (empty($aege_posts)) {
            echo '<p>No AEGE-enabled content found.</p>';
            return;
        }
        ?>
        <div class="aege-insights-table-wrapper">
            <h2><?php esc_html_e( 'Content Insights Breakdown', 'aege' ); ?></h2>
            <table class="wp-list-table widefat striped">
                <thead>
                    <tr>
                        <th><?php esc_html_e( 'Title', 'aege' ); ?></th>
                        <th style="width:10%;"><?php esc_html_e( 'AEO Score', 'aege' ); ?></th>
                        <th style="width:10%;"><?php esc_html_e( 'Citations', 'aege' ); ?></th>
                        <th style="width:10%;"><?php esc_html_e( 'Entities', 'aege' ); ?></th>
                        <th style="width:12%;"><?php esc_html_e( 'AEO Freshness', 'aege' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($aege_posts as $post) : ?>
                        <?php
                            // Each row needs an ID that matches the anchor from Phase 1
                            $score = get_post_meta($post->ID, '_aege_aeo_score', true);
                            $citations = get_post_meta($post->ID, '_aege_citation_count', true);
                            $entities = get_post_meta($post->ID, '_aege_entity_count', true);
                            $freshness = get_post_meta($post->ID, '_aege_llm_last_updated', true);
                        ?>
                        <tr id="post-<?php echo $post->ID; ?>">
                            <td>
                                <strong><a href="<?php echo get_edit_post_link($post->ID); ?>"><?php echo esc_html($post->post_title); ?></a></strong>
                                <div class="row-actions">
                                    <a href="<?php echo get_edit_post_link($post->ID); ?>">Edit</a> | 
                                    <a href="<?php echo get_permalink($post->ID) . 'llm/'; ?>" target="_blank" rel="noopener">View LLM Version</a>
                                </div>
                            </td>
                            <td><?php echo is_numeric($score) ? sprintf('%d%%', round($score)) : '—'; ?></td>
                            <td><?php echo is_numeric($citations) ? intval($citations) : '—'; ?></td>
                            <td><?php echo is_numeric($entities) ? intval($entities) : '—'; ?></td>
                            <td><?php echo $freshness ? date('Y-m-d', (int)$freshness) : '—'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php
    }

    public function register_settings() {
        register_setting(
            'aege_option_group', // Option group
            'aege_settings',     // Option name
            array( $this, 'sanitize_settings' ) // Sanitize callback
        );

        // --- General Settings Section ---
        add_settings_section(
            'aege_general_section',
            'General Settings',
            null,
            'aege-admin'
        );

        add_settings_field( 'master_switch', 'Plugin Master Switch', array( $this, 'render_master_switch_field' ), 'aege-admin', 'aege_general_section' );
        add_settings_field( 'post_types', 'Enable for Post Types', array( $this, 'render_post_types_field' ), 'aege-admin', 'aege_general_section' );

        // --- Content & Automation Section ---
        add_settings_section( 'aege_automation_section', 'Content & Automation', null, 'aege-admin' );
        add_settings_field( 'workflow', 'AEO Generation Workflow', array( $this, 'render_workflow_field' ), 'aege-admin', 'aege_automation_section' );
        add_settings_field( 'enable_summaries', 'Enable Key Takeaways', array( $this, 'render_enable_summaries_field' ), 'aege-admin', 'aege_automation_section' );
        add_settings_field( 'enable_citation_marking', 'Enable Citation Marking', array( $this, 'render_enable_citation_marking_field' ), 'aege-admin', 'aege_automation_section' );
        add_settings_field( 'citation_detection_mode', 'Citation Detection Mode', array( $this, 'render_citation_detection_mode_field' ), 'aege-admin', 'aege_automation_section' );
        add_settings_field( 'ai_enhancement', 'AI Content Enhancement (Beta)', array( $this, 'render_ai_enhancement_fields' ), 'aege-admin', 'aege_automation_section' );

        // --- Cache Settings Section ---
        add_settings_section( 'aege_cache_section', 'Cache Settings', null, 'aege-admin' );
        add_settings_field( 'cache_strategy', 'Cache Strategy', array( $this, 'render_cache_strategy_field' ), 'aege-admin', 'aege_cache_section' );
        add_settings_field( 'clear_cache', 'Clear Cache', array( $this, 'render_clear_cache_field' ), 'aege-admin', 'aege_cache_section' );

        // --- SEO Integration Section ---
        add_settings_section( 'aege_seo_integration_section', 'SEO Integration', null, 'aege-admin' );
        add_settings_field( 'seo_import_strategy', 'SEO Data Import Strategy', array( $this, 'render_seo_import_strategy_field' ), 'aege-admin', 'aege_seo_integration_section' );
        add_settings_field( 'schema_enhancement', 'Schema Enhancement', array( $this, 'render_schema_enhancement_field' ), 'aege-admin', 'aege_seo_integration_section' );
        
        // --- API Settings Section ---
        add_settings_section( 'aege_api_section', 'API Settings', null, 'aege-admin' );
        add_settings_field( 'enable_rest_api', 'Enable REST API', array( $this, 'render_enable_rest_api_field' ), 'aege-admin', 'aege_api_section' );
        
        // --- Logging Settings Section ---
        add_settings_section( 'aege_logging_section', 'Logging Settings', null, 'aege-admin' );
        add_settings_field( 'enable_logging', 'Enable Logging', array( $this, 'render_enable_logging_field' ), 'aege-admin', 'aege_logging_section' );
        add_settings_field( 'log_level', 'Log Level', array( $this, 'render_log_level_field' ), 'aege-admin', 'aege_logging_section' );
        add_settings_field( 'view_logs', 'View Logs', array( $this, 'render_view_logs_field' ), 'aege-admin', 'aege_logging_section' );
        
        // --- Summary Settings Section ---
        add_settings_section( 'aege_summary_section', 'Summary Settings', null, 'aege-admin' );
        add_settings_field( 'summary_important_words', 'Important Words', array( $this, 'render_summary_important_words_field' ), 'aege-admin', 'aege_summary_section' );
        add_settings_field( 'summary_stop_words', 'Stop Words', array( $this, 'render_summary_stop_words_field' ), 'aege-admin', 'aege_summary_section' );
        add_settings_field( 'summary_max_key_points', 'Max Key Points', array( $this, 'render_summary_max_key_points_field' ), 'aege-admin', 'aege_summary_section' );
        add_settings_field( 'summary_sentence_count', 'Sentence Count', array( $this, 'render_summary_sentence_count_field' ), 'aege-admin', 'aege_summary_section' );
        
        // --- File Management Section ---
        add_settings_section( 'aege_file_management_section', 'File Management', null, 'aege-admin' );
        add_settings_field( 'llms_file', 'llms.txt Management', array( $this, 'render_llms_file_field' ), 'aege-admin', 'aege_file_management_section' );
        add_settings_field( 'robots_file', 'robots.txt Management', array( $this, 'render_robots_file_field' ), 'aege-admin', 'aege_file_management_section' );
    }

    /**
     * Enqueues scripts and styles for the AEGE admin pages.
     *
     * @param string $hook The current admin page hook.
     */
    public function enqueue_admin_scripts($hook) {
    $screen = get_current_screen();
    if (!$screen) {
        return;
    }

    // --- Logic for the Post Editor & New Post Screens ---
    if ($hook === 'post.php' || $hook === 'post-new.php') {
        if (in_array($screen->post_type, ['post', 'page'])) {
            wp_enqueue_editor();
            
            $script_path = plugin_dir_path(__FILE__) . '../admin/js/aege-meta-box-editor.js';
            wp_enqueue_script(
                'aege-meta-box-editor-fix',
                plugin_dir_url(__FILE__) . '../admin/js/aege-meta-box-editor.js',
                ['jquery', 'wp-editor'],
                filemtime($script_path), // Automatic cache-busting
                true
            );
        }
    } 
    // --- Logic for your Dashboard Screens ---
    elseif ($hook === 'toplevel_page_aege' || $hook === 'aege_page_ge-analysis') {
        wp_register_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', [], '4.4.1', true);
        
        $dashboard_script_path = plugin_dir_path(__FILE__) . '../admin/js/aege-dashboard.js';
        wp_enqueue_script(
            'aege-dashboard-js',
            plugin_dir_url(__FILE__) . '../admin/js/aege-dashboard.js',
            ['jquery', 'chart-js'],
            filemtime($dashboard_script_path), // Automatic cache-busting
            true
        );
        
        wp_localize_script('aege-dashboard-js', 'aege_ajax_object', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('aege_dashboard_nonce')
        ]);
    }
}



    public function sanitize_settings( $input ) {
        /**
         * The definitive fix for the "undefined method" error.
         * We use the PHP magic constant __DIR__ to get the directory of the current file.
         * This ensures the path is always correct and has no dependency on constants
         * defined in other files, which may not be available during the WordPress
         * core settings save process.
         */
        require_once __DIR__ . '/class-aege-cache-manager.php';

        // When any settings are saved, trigger a full cache clear to apply changes globally.
        AEGE_Cache_Manager::clear_all_cache();

        // Flush rewrite rules when settings are saved to ensure LLM endpoints work
        flush_rewrite_rules();

        // --- Perform sanitization on the input ---
        $sanitized_input = array();

        // Sanitize master switch
        $sanitized_input['master_switch'] = ( isset( $input['master_switch'] ) && $input['master_switch'] == 1 ) ? 1 : 0;

        // Sanitize post types
        if ( ! empty( $input['post_types'] ) && is_array( $input['post_types'] ) ) {
            foreach ( $input['post_types'] as $post_type => $value ) {
                $sanitized_input['post_types'][ sanitize_key( $post_type ) ] = ( $value == 1 ) ? 1 : 0;
            }
        } else {
            $sanitized_input['post_types'] = array();
        }

        // Sanitize workflow choice
        $allowed_workflows = array( 'automated_override', 'manual_opt_in' );
        $sanitized_input['workflow'] = ( isset( $input['workflow'] ) && in_array( $input['workflow'], $allowed_workflows ) ) ? $input['workflow'] : 'automated_override';
        
        // Sanitize enable summaries setting
        $sanitized_input['enable_summaries'] = ( isset( $input['enable_summaries'] ) && $input['enable_summaries'] == 1 ) ? 1 : 0;
        
        // Sanitize enable citation marking setting
        $sanitized_input['enable_citation_marking'] = ( isset( $input['enable_citation_marking'] ) && $input['enable_citation_marking'] == 1 ) ? 1 : 0;
        
        // Sanitize citation detection mode setting
        $allowed_detection_modes = array( 'automatic', 'manual' );
        $sanitized_input['citation_detection_mode'] = ( isset( $input['citation_detection_mode'] ) && in_array( $input['citation_detection_mode'], $allowed_detection_modes ) ) ? $input['citation_detection_mode'] : 'automatic';
        
        // Sanitize AI enhancement fields (add more as you build them out)
        $sanitized_input['ai_enhancement'] = ( isset( $input['ai_enhancement'] ) && $input['ai_enhancement'] == 1 ) ? 1 : 0;

        // Sanitize cache strategy
        $allowed_cache_strategies = array( 'smart_cache', 'aggressive_cache', 'no_cache' );
        $sanitized_input['cache_strategy'] = ( isset( $input['cache_strategy'] ) && in_array( $input['cache_strategy'], $allowed_cache_strategies ) ) ? $input['cache_strategy'] : 'smart_cache';

        // Sanitize SEO import strategy
        $allowed_seo_import_strategies = array( 'enhance', 'preserve', 'replace' );
        $sanitized_input['seo_import_strategy'] = ( isset( $input['seo_import_strategy'] ) && in_array( $input['seo_import_strategy'], $allowed_seo_import_strategies ) ) ? $input['seo_import_strategy'] : 'enhance';

        // Sanitize schema enhancement
        $allowed_schema_enhancements = array( 'full', 'minimal', 'none' );
        $sanitized_input['schema_enhancement'] = ( isset( $input['schema_enhancement'] ) && in_array( $input['schema_enhancement'], $allowed_schema_enhancements ) ) ? $input['schema_enhancement'] : 'full';

        // Sanitize REST API setting
        $sanitized_input['enable_rest_api'] = ( isset( $input['enable_rest_api'] ) && $input['enable_rest_api'] == 1 ) ? 1 : 0;
        
        // Sanitize logging settings
        $sanitized_input['enable_logging'] = ( isset( $input['enable_logging'] ) && $input['enable_logging'] == 1 ) ? 1 : 0;
        
        $allowed_log_levels = array( 'emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug' );
        $sanitized_input['log_level'] = ( isset( $input['log_level'] ) && in_array( $input['log_level'], $allowed_log_levels ) ) ? $input['log_level'] : 'error';

        // Sanitize summary settings
        $sanitized_input['summary_important_words'] = sanitize_text_field($input['summary_important_words'] ?? '');
        $sanitized_input['summary_stop_words'] = sanitize_text_field($input['summary_stop_words'] ?? '');
        $sanitized_input['summary_max_key_points'] = absint($input['summary_max_key_points'] ?? 5);
        $sanitized_input['summary_sentence_count'] = absint($input['summary_sentence_count'] ?? 3);

        // Return the sanitized array.
        return $sanitized_input;
    }

    // --- RENDER FUNCTIONS FOR FIELDS ---

    public function render_master_switch_field() {
        ?>
        <input type="checkbox" id="master_switch" name="aege_settings[master_switch]" value="1" <?php checked( isset( $this->options['master_switch'] ) && $this->options['master_switch'] == 1 ); ?> />
        <p class="description"><?php esc_html_e('This master switch controls the entire plugin. Deactivating this will disable all AEGE functionality.', 'aege'); ?></p>
        <?php
    }

    public function render_post_types_field() {
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        foreach ( $post_types as $post_type ) {
            $checked = isset( $this->options['post_types'][$post_type->name] ) ? 'checked' : '';
            ?>
            <label><input type="checkbox" name="aege_settings[post_types][<?php echo esc_attr($post_type->name); ?>]" value="1" <?php checked(1, $this->options['post_types'][$post_type->name] ?? 0, false); ?> /> <?php echo esc_html($post_type->label); ?></label><br>
            <?php
        }
        ?>
        <p class="description"><?php esc_html_e('Choose which content types you want to enable AEGE capabilities for.', 'aege'); ?></p>
        <?php
    }

    public function render_workflow_field() {
        $workflow = isset( $this->options['workflow'] ) ? $this->options['workflow'] : 'automated_override';
        ?>
        <label><input type="radio" name="aege_settings[workflow]" value="automated_override" <?php checked($workflow, 'automated_override'); ?>> <?php esc_html_e( 'Automated with Manual Override (Recommended)', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'The plugin automatically generates an AI-optimized version at /llm/ endpoint for each post without modifying your original content. Adds an editor to each post for optional fine-tuning of the LLM version.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[workflow]" value="manual_opt_in" <?php checked($workflow, 'manual_opt_in'); ?>> <?php esc_html_e( 'Manual Opt-in Only', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'The plugin does nothing by default. You must manually enable AEGE for each post to create AI-optimized versions at /llm/ endpoints.', 'aege' ); ?></p>
        <?php
    }

    public function render_enable_summaries_field() {
        ?>
        <input type="checkbox" id="enable_summaries" name="aege_settings[enable_summaries]" value="1" <?php checked( isset( $this->options['enable_summaries'] ) && $this->options['enable_summaries'] == 1 ); ?> />
        <label for="enable_summaries"><strong><?php esc_html_e('Enable Key Takeaways', 'aege'); ?></strong></label>
        <p class="description"><?php esc_html_e('Enable this to automatically generate key takeaways and summaries for AEGE-optimized content. These appear at the top of the LLM version to help AI systems quickly understand the main points.', 'aege'); ?></p>
        <?php
    }

    public function render_enable_citation_marking_field() {
        ?>
        <input type="checkbox" id="enable_citation_marking" name="aege_settings[enable_citation_marking]" value="1" <?php checked( isset( $this->options['enable_citation_marking'] ) && $this->options['enable_citation_marking'] == 1 ); ?> />
        <label for="enable_citation_marking"><strong><?php esc_html_e('Enable Citation Marking', 'aege'); ?></strong></label>
        <p class="description"><?php esc_html_e('Enable this to allow marking of citation links with semantic attributes. Links with class="citation" will be enhanced with additional markup for better source tracking.', 'aege'); ?></p>
        <?php
    }
    
    /**
     * Render citation detection mode field
     */
    public function render_citation_detection_mode_field() {
        $options = get_option('aege_settings');
        $mode = isset($options['citation_detection_mode']) ? $options['citation_detection_mode'] : 'automatic';
        ?>
        <fieldset>
        <label>
        <input type="radio" name="aege_settings[citation_detection_mode]" value="automatic" <?php checked($mode, 'automatic'); ?> /> 
        <strong><?php esc_html_e('Automatic (Recommended)', 'aege'); ?></strong>: <?php esc_html_e('Treat all external links as citations by default. You can exclude specific links by adding <code>class="no-citation"</code> to them.', 'aege'); ?>
        </label><br>
        <label>
        <input type="radio" name="aege_settings[citation_detection_mode]" value="manual" <?php checked($mode, 'manual'); ?> /> 
        <strong><?php esc_html_e('Manual', 'aege'); ?></strong>: <?php esc_html_e('Only treat links as citations if they have <code>class="citation"</code>. (Current behavior)', 'aege'); ?>
        </label>
        </fieldset>
        
        <p class="description"><?php esc_html_e('Choose how AEGE detects citations in your content. Automatic mode provides immediate citation metrics for most users.', 'aege'); ?></p>
        <?php
    }

    public function render_ai_enhancement_fields() {
        // This would contain the complex UI for AI provider, API key, model, and prompt
        // For brevity in this prototype, we'll just put a placeholder.
        ?>
        <input type="checkbox" id="ai_enhancement" name="aege_settings[ai_enhancement]" value="1" <?php checked( isset( $this->options['ai_enhancement'] ) && $this->options['ai_enhancement'] == 1 ); ?> />
        <label for="ai_enhancement"><strong><?php esc_html_e('Enable AI Content Enhancement', 'aege'); ?></strong></label>
        <p class="description"><?php esc_html_e('Enable this to use an external AI service to automatically restructure your content. Requires an API key and may incur costs.', 'aege'); ?></p>
        <?php
        // The full UI for API keys and models would be rendered here if the box is checked.
    }

    public function render_cache_strategy_field() {
        $cache_strategy = isset( $this->options['cache_strategy'] ) ? $this->options['cache_strategy'] : 'smart_cache';
        ?>
        <label><input type="radio" name="aege_settings[cache_strategy]" value="smart_cache" <?php checked($cache_strategy, 'smart_cache'); ?>> <?php esc_html_e( 'Smart Cache (24 hours)', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Cache AEGE pages for 24 hours. Good balance between performance and freshness.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[cache_strategy]" value="aggressive_cache" <?php checked($cache_strategy, 'aggressive_cache'); ?>> <?php esc_html_e( 'Aggressive Cache (1 week)', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Cache AEGE pages for 1 week. Best for high-traffic sites with infrequently updated content.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[cache_strategy]" value="no_cache" <?php checked($cache_strategy, 'no_cache'); ?>> <?php esc_html_e( 'No Cache (1 minute)', 'aege' ); ?></label><br>
        <p class="description">' . __( 'Cache AEGE pages for only 1 minute. Always serve the most recent version, but with higher server load.', 'aege' ) . '</p>
        <?php
    }

    public function render_clear_cache_field() {
        $clear_cache_url = admin_url('admin.php?page=aege&aege_action=clear_cache');
        ?>
        <p><a href="<?php echo esc_url($clear_cache_url); ?>" class="button button-secondary"><?php esc_html_e('Clear AEGE Cache Now', 'aege'); ?></a></p>
        <p class="description"><?php esc_html_e('Click this button to clear all AEGE cached pages. This is useful when you make changes to your content or settings.', 'aege'); ?></p>
        <?php
    }

    public function render_seo_import_strategy_field() {
        $seo_import_strategy = isset( $this->options['seo_import_strategy'] ) ? $this->options['seo_import_strategy'] : 'enhance';
        ?>
        <label><input type="radio" name="aege_settings[seo_import_strategy]" value="enhance" <?php checked($seo_import_strategy, 'enhance'); ?>> <?php esc_html_e( 'Enhance SEO Data', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Import SEO data from plugins and enhance it with AEGE-specific optimizations.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[seo_import_strategy]" value="preserve" <?php checked($seo_import_strategy, 'preserve'); ?>> <?php esc_html_e( 'Preserve SEO Data', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Import SEO data from plugins without modification.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[seo_import_strategy]" value="replace" <?php checked($seo_import_strategy, 'replace'); ?>> <?php esc_html_e( 'Replace with AEGE Schema', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Ignore SEO plugin data and generate AEGE-optimized schema from scratch.', 'aege' ); ?></p>
        <?php
    }

    public function render_schema_enhancement_field() {
        $schema_enhancement = isset( $this->options['schema_enhancement'] ) ? $this->options['schema_enhancement'] : 'full';
        ?>
        <label><input type="radio" name="aege_settings[schema_enhancement]" value="full" <?php checked($schema_enhancement, 'full'); ?>> <?php esc_html_e( 'Full Enhancement', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Add all AEGE-specific schema enhancements to imported data.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[schema_enhancement]" value="minimal" <?php checked($schema_enhancement, 'minimal'); ?>> <?php esc_html_e( 'Minimal Enhancement', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Only add essential AEGE schema enhancements to prevent conflicts.', 'aege' ); ?></p>
        <label><input type="radio" name="aege_settings[schema_enhancement]" value="none" <?php checked($schema_enhancement, 'none'); ?>> <?php esc_html_e( 'No Enhancement', 'aege' ); ?></label><br>
        <p class="description"><?php esc_html_e( 'Use imported schema data as-is without any AEGE enhancements.', 'aege' ); ?></p>
        <?php
    }

    public function render_enable_rest_api_field() {
        ?>
        <input type="checkbox" id="enable_rest_api" name="aege_settings[enable_rest_api]" value="1" <?php checked( isset( $this->options['enable_rest_api'] ) && $this->options['enable_rest_api'] == 1 ); ?> />
        <label for="enable_rest_api"><strong><?php esc_html_e('Enable AEGE REST API', 'aege'); ?></strong></label>
        <p class="description"><?php esc_html_e('Enable this to allow external services to interact with the plugin via REST API. For security, this is disabled by default. Only enable if you plan to use external LLM integrations.', 'aege'); ?></p>
        <?php
    }

    public function render_enable_logging_field() {
        ?>
        <input type="checkbox" id="enable_logging" name="aege_settings[enable_logging]" value="1" <?php checked( isset( $this->options['enable_logging'] ) && $this->options['enable_logging'] == 1 ); ?> />
        <label for="enable_logging"><strong><?php esc_html_e('Enable Logging', 'aege'); ?></strong></label>
        <p class="description"><?php esc_html_e('Enable this to log plugin events for debugging and monitoring. Logs are stored in the WordPress uploads directory.', 'aege'); ?></p>
        <?php
    }

    public function render_log_level_field() {
        $log_level = isset( $this->options['log_level'] ) ? $this->options['log_level'] : 'error';
        ?>
        <label><input type="radio" name="aege_settings[log_level]" value="emergency" <?php checked($log_level, 'emergency'); ?>> <?php esc_html_e( 'Emergency', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="alert" <?php checked($log_level, 'alert'); ?>> <?php esc_html_e( 'Alert', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="critical" <?php checked($log_level, 'critical'); ?>> <?php esc_html_e( 'Critical', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="error" <?php checked($log_level, 'error'); ?>> <?php esc_html_e( 'Error (Default)', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="warning" <?php checked($log_level, 'warning'); ?>> <?php esc_html_e( 'Warning', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="notice" <?php checked($log_level, 'notice'); ?>> <?php esc_html_e( 'Notice', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="info" <?php checked($log_level, 'info'); ?>> <?php esc_html_e( 'Info', 'aege' ); ?></label><br>
        <label><input type="radio" name="aege_settings[log_level]" value="debug" <?php checked($log_level, 'debug'); ?>> <?php esc_html_e( 'Debug', 'aege' ); ?></label><br>
        <p class="description">' . __( 'Select the minimum log level to record. Emergency is the most critical, Debug is the most verbose.', 'aege' ) . '</p>
        <?php
    }

    public function render_view_logs_field() {
        $log_size = AEGE_Logger::get_log_size();
        $log_size_formatted = size_format($log_size, 2);
        $logs_url = admin_url('admin.php?page=aege&aege_action=view_logs');
        $clear_logs_url = admin_url('admin.php?page=aege&aege_action=clear_logs');
        ?>
        <p><?php esc_html_e( 'Current log file size:', 'aege' ); ?> <?php echo esc_html($log_size_formatted); ?></p>
        <div class="aege-logs-actions">
            <a href="<?php echo esc_url($logs_url); ?>" class="button" target="_blank"><?php esc_html_e('View Logs', 'aege'); ?></a>
            <a href="<?php echo esc_url($clear_logs_url); ?>" class="button"><?php esc_html_e('Clear Logs', 'aege'); ?></a>
        </div>
        <p class="description"><?php esc_html_e('View or clear the plugin logs. Logs are rotated when they exceed 5MB.', 'aege'); ?></p>
        <?php
    }

    public function render_summary_important_words_field() {
        $important_words = isset( $this->options['summary_important_words'] ) ? $this->options['summary_important_words'] : '';
        ?>
        <input type="text" name="aege_settings[summary_important_words]" value="<?php echo esc_attr($important_words); ?>" class="regular-text" />
        <p class="description"><?php esc_html_e('Comma-separated list of important words for summary generation.', 'aege'); ?></p>
        <?php
    }

    public function render_summary_stop_words_field() {
        $stop_words = isset( $this->options['summary_stop_words'] ) ? $this->options['summary_stop_words'] : '';
        ?>
        <input type="text" name="aege_settings[summary_stop_words]" value="<?php echo esc_attr($stop_words); ?>" class="regular-text" />
        <p class="description"><?php esc_html_e('Comma-separated list of stop words to exclude from summary generation.', 'aege'); ?></p>
        <?php
    }

    public function render_summary_max_key_points_field() {
        $max_key_points = isset( $this->options['summary_max_key_points'] ) ? $this->options['summary_max_key_points'] : 5;
        ?>
        <input type="number" name="aege_settings[summary_max_key_points]" value="<?php echo esc_attr($max_key_points); ?>" class="small-text" min="1" />
        <p class="description"><?php esc_html_e('Maximum number of key points to extract for summaries.', 'aege'); ?></p>
        <?php
    }

    public function render_summary_sentence_count_field() {
        $sentence_count = isset( $this->options['summary_sentence_count'] ) ? $this->options['summary_sentence_count'] : 3;
        ?>
        <input type="number" name="aege_settings[summary_sentence_count]" value="<?php echo esc_attr($sentence_count); ?>" class="small-text" min="1" />
        <p class="description"><?php esc_html_e('Number of sentences to include in the generated summary.', 'aege'); ?></p>
        <?php
    }

    public function render_llms_file_field() {
        $llms_content = $this->get_llms_file_content();
        ?>
        <p class="description"><?php esc_html_e('The <code>llms.txt</code> file guides AI crawlers to your most valuable content. The plugin will attempt to create this file automatically.', 'aege'); ?></p>
        
        <textarea readonly rows="10" cols="50" class="large-text code"><?php echo esc_textarea($llms_content); ?></textarea>
        
        <p><button type="button" class="button button-secondary" id="aege-create-llms-btn"><?php esc_html_e('Create llms.txt File', 'aege'); ?></button></p>
        <p id="aege-llms-status" class="description"></p>
        <?php
    }

/**
 * Renders a read-only textarea with the recommended robots.txt directives.
 */
public function render_robots_file_field() {
    // Get the sitemap URL once. The redundant if/else block has been removed.
    $sitemap_url = home_url('/aege-sitemap.xml');

    // Use a PHP "Heredoc" to build the multi-line string. This is the cleanest and
    // most reliable way to handle this. It preserves newlines and allows variables.
    $robots_content = <<<ROBOTS
# AEGE Recommendations
User-agent: GPTBot
Allow: /

User-agent: Google-Extended
Allow: /

User-agent: ClaudeBot
Allow: /

User-agent: CCBot
Allow: /

User-agent: PerplexityBot
Allow: /

User-agent: Anthropic-AI
Allow: /

User-agent: OAI-SearchBot
Allow: /

User-agent: cohere-ai
Allow: /

User-agent: FacebookBot
Allow: /

# AEGE Sitemap
Sitemap: {$sitemap_url}
ROBOTS;

    // Now, cleanly output the HTML and the content.
    ?>
    <p class="description"><?php esc_html_e('For best results, add the following lines to your robots.txt file:', 'aege'); ?></p>
    <textarea readonly rows="15" class="large-text code"><?php echo esc_textarea($robots_content); ?></textarea>
    <p class="description"><?php esc_html_e('This helps major LLM crawlers find and properly index your AEGE-optimized content.', 'aege'); ?></p>
    <?php
}
    private function get_aege_enabled_urls() {
        $urls = array();
        $args = array(
            'post_type' => array_keys($this->options['post_types']),
            'posts_per_page' => -1,
            'meta_key' => '_aege_enabled',
            'meta_value' => '1'
        );
        $query = new WP_Query($args);
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $urls[] = trailingslashit( get_permalink() ) . 'llm/';
            }
        }
        wp_reset_postdata();
        return $urls;
    }

    // --- METABOX FUNCTIONS ---

    public function add_meta_box() {
        $workflow = isset($this->options['workflow']) ? $this->options['workflow'] : 'automated_override';
        $enabled_post_types = isset($this->options['post_types']) ? array_keys($this->options['post_types']) : array();

        if ($workflow === 'manual_opt_in') {
            add_meta_box(
                'aege_metabox',
                'AEGE Optimization',
                array( $this, 'render_meta_box' ),
                $enabled_post_types,
                'advanced', // Use 'advanced' for best compatibility with both editors
                'high'
            );

            // Make the metabox closed by default for all users
            add_filter('default_hidden_meta_boxes', array($this, 'hide_metabox_by_default'), 10, 2);
        } else {
            // For automated workflow, always show the meta box
            add_meta_box(
                'aege_llm_meta_box',
                __('AEGE Optimization', 'aege'),
                array( $this, 'render_llm_meta_box' ),
                array('post', 'page'),
                'advanced', // Use 'advanced' for best compatibility with both editors
                'high'
            );
        }
    }


    /**
     * Hide the AEGE metabox by default for all users
     */
    public function hide_metabox_by_default($hidden, $screen) {
        // Only apply to post and page edit screens
        if (isset($screen->post_type) && in_array($screen->post_type, ['post', 'page'])) {
            // Add our metabox to the hidden list if it's not already there
            if (!in_array('aege_metabox', $hidden)) {
                $hidden[] = 'aege_metabox';
            }
        }
        return $hidden;
    }

    /**
     * Renders the content for the AEGE Optimization meta box.
     * This function contains the wp_editor, action buttons, and the necessary HTML wrappers for our JavaScript to function.
     */
    public function render_meta_box($post) {
        // Get the previously saved values
        $llm_content = get_post_meta($post->ID, '_aege_custom_content', true);
        $aege_enabled = get_post_meta($post->ID, '_aege_enabled', true);

        // Add security nonce
        wp_nonce_field('aege_meta_box', 'aege_meta_box_nonce');

        // --- Enable/Disable Checkbox ---
        ?>
        <div style="margin-bottom: 10px;">
            <label for="aege_enabled">
                <input type="checkbox" id="aege_enabled" name="aege_enabled" value="1" <?php checked($aege_enabled, '1'); ?> />
                <strong><?php esc_html_e('Enable AEGE "llm" for this post', 'aege'); ?></strong>
            </label>
        </div>

        <!-- Custom Fields Container (always visible, WordPress handles metabox toggle) -->
        <div id="aege-custom-fields">

            <!-- Status Messages Container -->
            <div id="aege-status-messages" style="margin-bottom: 10px;"></div>

            <!-- Start of the essential HTML wrapper -->
            <!-- The JavaScript uses this div to replace the editor content after an AJAX save. -->
            <div id="aege_llm_content_editor_container">
                <?php
                // Define the settings for our custom wp_editor instance.
                $editor_settings = [
                    'textarea_name' => 'aege_llm_content', // The name attribute for the textarea
                    'media_buttons' => false,              // We don't need the "Add Media" button
                    'textarea_rows' => 10,                 // The height of the editor
                    'tinymce'       => [
                        // This adds the placeholder text to the Visual editor.
                        'placeholder' => __('Leave empty to auto-generate from the main content. Fill this in to create a custom, fine-tuned version for AI consumption.', 'aege')
                    ],
                ];

                // Output the WordPress editor.
                wp_editor($llm_content, 'aege_llm_content', $editor_settings);
                ?>
            </div>

            <!-- End of the essential HTML wrapper -->

            <!-- Security Nonce for AJAX -->
            <!-- This is a hidden security field. Our JavaScript sends this back to the server -->
            <!-- to verify that the AJAX request is legitimate and not a security risk. -->
            <?php wp_nonce_field('aege_llm_editor_nonce', 'aege_llm_editor_nonce'); ?>

            <!-- Action Buttons -->
            <div class="aege-metabox-actions" style="margin-top: 15px;">
                <!-- Import Content button -->
                <button type="button" class="button" id="aege-copy-original" title="<?php echo esc_attr__('Copy content from the main post editor', 'aege'); ?>">
                    <?php esc_html_e('Import Content', 'aege'); ?>
                </button>

                <!-- Create/Update LLM button (only show for published posts) -->
                <?php if ($post->post_status === 'publish') : ?>
                    <button type="button" class="button button-primary" id="aege-publish-llm" title="<?php echo esc_attr__('Updates only the AI-optimized version without affecting the main post', 'aege'); ?>">
                        <?php esc_html_e('Create/Update LLM', 'aege'); ?>
                    </button>
                <?php endif; ?>

                <!-- Preview button -->
                <?php
                $preview_url = get_preview_post_link($post->ID);
                if ($post->post_status !== 'publish') {
                    $preview_url = add_query_arg('is_aege_page', '1', $preview_url);
                } else {
                    $preview_url = trailingslashit(get_permalink($post->ID)) . 'llm/';
                }
                ?>
                <button type="button" class="button" id="aege-preview" data-url="<?php echo esc_url($preview_url); ?>" title="<?php echo esc_attr__('Preview the AEGE-optimized version', 'aege'); ?>">
                    <?php esc_html_e('Preview', 'aege'); ?>
                </button>
            </div>
            <!-- End aege-metabox-actions -->

        </div>
        <!-- End aege-custom-fields -->
        <?php
    }


    /**
     * AJAX callback to re-render the LLM wp_editor.
     */
    public function ajax_refresh_llm_editor_callback() {
        check_ajax_referer('aege_llm_editor_nonce', 'security');
        $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
        if (!$post_id || !$post = get_post($post_id)) {
            wp_die();
        }
        $this->render_llm_meta_box($post);
        wp_die();
    }


    public function save_meta_box_data( $post_id ) {
        if ( ! isset( $_POST['aege_meta_box_nonce'] ) || ! wp_verify_nonce( $_POST['aege_meta_box_nonce'], 'aege_meta_box' ) ) {
            return;
        }
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Save the enabled/disabled status
        if ( isset( $_POST['aege_enabled'] ) ) {
            update_post_meta( $post_id, '_aege_enabled', '1' );
        } else {
            delete_post_meta( $post_id, '_aege_enabled' );
        }

        // Save the custom content editor if it exists
        if ( isset( $_POST['aege_llm_content'] ) ) {
            update_post_meta( $post_id, '_aege_custom_content', wp_kses_post( $_POST['aege_llm_content'] ) );
        }
        
        // Generate AEGE content for analysis
        $custom_content = get_post_meta($post_id, '_aege_custom_content', true);
        if (empty($custom_content)) {
            // Generate content using existing cleaner
            if (!class_exists('AEGE_Content_Cleaner')) {
                require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
            }
            $content_cleaner = new AEGE_Content_Cleaner();
            $content = apply_filters('the_content', get_post($post_id)->post_content);
            $custom_content = $content_cleaner->clean($content);
        }
        
        // Calculate and save citation count
        $citation_count = $this->calculate_citation_count($custom_content);
        update_post_meta($post_id, '_aege_citation_count', $citation_count);
        
        // Detect and save schemas
        if (!class_exists('AEGE_Content_Cleaner')) {
            require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
        }
        $content_cleaner = new AEGE_Content_Cleaner();
        $schemas = $content_cleaner->detect_and_generate_schemas($custom_content, $post_id);
        update_post_meta($post_id, '_aege_schemas', $schemas);
        
        // Calculate and save entity count
        if (!class_exists('AEGE_Entity_Analyzer')) {
            require_once AEGE_PLUGIN_DIR . 'includes/class-aege-entity-analyzer.php';
        }
        if (class_exists('AEGE_Entity_Analyzer')) {
            $entity_analyzer = new AEGE_Entity_Analyzer();
            $entity_count = $entity_analyzer->count_entities($custom_content);
            update_post_meta($post_id, '_aege_entity_count', $entity_count);
        }
        
        // Calculate and save structural elements count
        $structural_count = $this->count_structural_elements($custom_content);
        update_post_meta($post_id, '_aege_structural_count', $structural_count);
        
        // Calculate and save AEO score
        $aeo_score = $this->calculate_aeo_score($post_id);
        update_post_meta($post_id, '_aege_aeo_score', $aeo_score);
        
        // Set AEO freshness timestamp
        update_post_meta($post_id, '_aege_llm_last_updated', get_post_modified_time('U', false, $post_id));
        
        // When a post is saved, clear its AEGE cache
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-cache-manager.php';
        AEGE_Cache_Manager::clear_cache_for_object($post_id, 'post');
        
        // Schedule llms.txt file regeneration with debounce
        if ( ! wp_next_scheduled('aege_schedule_llms_regeneration') ) {
            wp_schedule_single_event(time() + (5 * MINUTE_IN_SECONDS), 'aege_schedule_llms_regeneration');
        }
        
        // Clear dashboard cache
        $this->clear_dashboard_cache();
    }

    /**
     * Handle cache clearing actions from the admin page
     */
    private function handle_cache_clearing_actions() {
        // Handle cache clearing
        if (isset($_GET['aege_action']) && $_GET['aege_action'] === 'clear_cache' && current_user_can('manage_options')) {
            if (class_exists('AEGE_Cache_Manager')) {
                AEGE_Cache_Manager::clear_all_cache();
                add_settings_error('aege_messages', 'aege_cache_cleared', 'AEGE cache cleared successfully.', 'updated');
            }
        }
        
        // Handle page cache clearing
        if (isset($_GET['aege_action']) && $_GET['aege_action'] === 'clear_page_cache' && current_user_can('manage_options')) {
            if (class_exists('AEGE_Cache_Manager')) {
                AEGE_Cache_Manager::clear_all_page_caches();
                add_settings_error('aege_messages', 'aege_page_cache_cleared', 'Page caches cleared successfully.', 'updated');
            }
        }
    }
    
    /**
     * Get AEGE-enabled posts with detailed information
     */
    private function get_aege_enabled_posts_with_details($post_types) {
        if (empty($post_types)) {
            return array();
        }
        
        // Check the workflow setting
        $workflow = isset($this->options['workflow']) ? $this->options['workflow'] : 'automated_override';
        
        $args = array(
            'post_type' => $post_types,
            'post_status' => 'publish',
            'posts_per_page' => -1
        );
        
        // If not using automated workflow, only include explicitly enabled posts
        if ($workflow !== 'automated_override') {
            $args['meta_query'] = array(
                array(
                    'key' => '_aege_enabled',
                    'value' => '1',
                    'compare' => '='
                )
            );
        }
        
        $posts = get_posts($args);
        
        // For automated workflow, all published posts are considered AEGE-enabled
        // For manual workflow, we need to filter explicitly enabled posts
        if ($workflow === 'automated_override') {
            return $posts;
        } else {
            // Already filtered by meta_query above
            return $posts;
        }
    }
    
    /**
     * Get AEGE-enabled tags
     */
    private function get_aege_enabled_tags() {
        // For now, we'll include all tags if the post type is enabled
        // In the future, we might want to add tag-specific AEGE enablement
        $options = get_option('aege_settings');
        if (isset($options['post_types']['post']) && $options['post_types']['post'] == 1) {
            return get_tags(array('hide_empty' => false));
        }
        return array();
    }
    
    /**
     * Get AEGE enabled content count
     *
     * @return int AEGE enabled content count
     */
    private function get_aege_enabled_count() {
        global $wpdb;
        $options = get_option('aege_settings');
        $enabled_post_types = isset($options['post_types']) ? array_keys($options['post_types']) : [];

        if (empty($enabled_post_types)) {
            return 0;
        }

        $post_types_placeholder = implode(', ', array_fill(0, count($enabled_post_types), '%s'));

        $sql = $wpdb->prepare("
            SELECT COUNT(p.ID)
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type IN ($post_types_placeholder)
            AND p.post_status = 'publish'
            AND pm.meta_key = '_aege_enabled'
            AND pm.meta_value = '1'
        ", ...$enabled_post_types); // Spread the array of values

        return (int) $wpdb->get_var($sql);
    }
    
    /**
     * Get total content count
     *
     * @return int Total content count
     */
    private function get_total_content_count() {
        $options = get_option('aege_settings');
        $enabled_post_types = isset($options['post_types']) ? array_keys($options['post_types']) : array();
        
        $total_posts = 0;
        foreach ($enabled_post_types as $post_type) {
            $total_posts += wp_count_posts($post_type)->publish;
        }
        return $total_posts;
    }
    
    /**
     * Calculate citation count from content
     *
     * @param string $content The content to analyze
     * @return int Number of citations
     */
    private function calculate_citation_count($content) {
        if (!class_exists('AEGE_Citation_Analyzer')) {
            require_once AEGE_PLUGIN_DIR . 'includes/class-aege-citation-analyzer.php';
        }
        
        $analyzer = new AEGE_Citation_Analyzer();
        return $analyzer->calculate_citation_count($content);
    }
    
    /**
     * Count structural elements in content
     *
     * @param string $content The content to analyze
     * @return int Number of structural elements
     */
    private function count_structural_elements($content) {
        if (empty($content)) {
            return 0;
        }

        $count = 0;

        // Count headings (H1-H6)
        $count += preg_match_all('/<h[1-6][^>]*>/i', $content);

        // Count lists (UL, OL)
        $count += preg_match_all('/<(ul|ol)[^>]*>/i', $content);

        // Count tables
        $count += preg_match_all('/<table[^>]*>/i', $content);

        // Count blockquotes
        $count += preg_match_all('/<blockquote[^>]*>/i', $content);

        // Count images with alt text (important for accessibility)
        $count += preg_match_all('/<img[^>]+alt=["\'][^"\']*["\'][^>]*>/i', $content);

        // Count paragraphs (but limit to avoid inflating score)
        $paragraph_count = preg_match_all('/<p[^>]*>/i', $content);
        $count += min(10, $paragraph_count); // Cap at 10 to avoid paragraph spam

        return $count;
    }

    /**
     * Clear dashboard cache
     */
    public function clear_dashboard_cache() {
        delete_transient('aege_dashboard_stats');
    }
    
    /**
     * Bulk update AEGE metadata for all enabled posts (batch processing)
     * 
     * This function calculates and stores all required AEGE metadata for existing content.
     * Uses batch processing to handle large numbers of posts without server overload.
     * 
     * @param int $offset Starting offset for batch processing
     * @param int $limit Number of posts to process in this batch
     * @return array Processing results
     */
    public function bulk_update_aege_metadata($offset = 0, $limit = 50) {
        // Check permissions
        if (!current_user_can('manage_options')) {
            AEGE_Logger::warning('Insufficient permissions for bulk AEGE metadata update', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            return array('success' => false, 'message' => 'Insufficient permissions');
        }
        
        AEGE_Logger::info('Starting batch AEGE metadata update', array(
            'offset' => $offset,
            'limit' => $limit
        ));
        
        // Get all AEGE-enabled posts (batch)
        $options = get_option('aege_settings');
        $enabled_post_types = isset($options['post_types']) ? array_keys($options['post_types']) : array('post');
        
        $args = array(
            'post_type' => $enabled_post_types,
            'posts_per_page' => $limit,
            'offset' => $offset,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_aege_enabled',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );
        
        $query = new WP_Query($args);
        $processed_count = 0;
        $errors = array();
        
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();
                $post = get_post($post_id);
                
                try {
                    AEGE_Logger::debug('Processing post for batch metadata update', array(
                        'post_id' => $post_id,
                        'post_title' => get_the_title()
                    ));
                    
                    // Get custom content or generate it
                    $custom_content = get_post_meta($post_id, '_aege_custom_content', true);
                    if (empty($custom_content)) {
                        // Apply content filters
                        $content = apply_filters('the_content', $post->post_content);
                        
                        // Clean content using AEGE content cleaner if available
                        if (class_exists('AEGE_Content_Cleaner')) {
                            $content_cleaner = new AEGE_Content_Cleaner();
                            $custom_content = $content_cleaner->clean($content);
                        } else {
                            // Fallback to basic content cleaning
                            $custom_content = wp_kses_post($content);
                        }
                    } else {
                        // Even when custom content exists, we still need to process it through the content cleaner
                        // to ensure citations are properly marked, security attributes are added, etc.
                        if (class_exists('AEGE_Content_Cleaner')) {
                            $content_cleaner = new AEGE_Content_Cleaner();
                            $custom_content = $content_cleaner->clean($custom_content);
                        } else {
                            // Fallback to basic content cleaning
                            $custom_content = wp_kses_post($custom_content);
                        }
                    }
                    
                    // Calculate and save citation count
                    $citation_count = $this->calculate_citation_count($custom_content);
                    update_post_meta($post_id, '_aege_citation_count', $citation_count);
                    
                    // Detect and save schemas (using existing function)
                    if (!class_exists('AEGE_Content_Cleaner')) {
                        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
                    }
                    $content_cleaner = new AEGE_Content_Cleaner();
                    $schemas = $content_cleaner->detect_and_generate_schemas($custom_content, $post_id);
                    update_post_meta($post_id, '_aege_schemas', $schemas);
                    
                    // Calculate and save entity count
                    if (!class_exists('AEGE_Entity_Analyzer')) {
                        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-entity-analyzer.php';
                    }
                    if (class_exists('AEGE_Entity_Analyzer')) {
                        $entity_analyzer = new AEGE_Entity_Analyzer();
                        $entity_count = $entity_analyzer->count_entities($custom_content);
                        update_post_meta($post_id, '_aege_entity_count', $entity_count);
                    }
                    
                    // Calculate and save structural elements count
                    $structural_count = $this->count_structural_elements($custom_content);
                    update_post_meta($post_id, '_aege_structural_count', $structural_count);
                    
                    // Calculate and save AEO score
                    $aeo_score = $this->calculate_aeo_score($post_id);
                    update_post_meta($post_id, '_aege_aeo_score', $aeo_score);
                    
                    // Set AEO freshness timestamp if not already set
                    $last_updated = get_post_meta($post_id, '_aege_llm_last_updated', true);
                    if (empty($last_updated)) {
                        update_post_meta($post_id, '_aege_llm_last_updated', get_post_modified_time('U', false, $post_id));
                    }
                    
                    $processed_count++;
                } catch (Exception $e) {
                    $errors[] = array(
                        'post_id' => $post_id,
                        'error' => $e->getMessage()
                    );
                    AEGE_Logger::error('Error processing post for bulk metadata update', array(
                        'post_id' => $post_id,
                        'error' => $e->getMessage()
                    ));
                }
            }
        }
        
        wp_reset_postdata();
        
        // Clear dashboard cache only on final batch
        if ($processed_count > 0 && empty($errors)) {
            $this->clear_dashboard_cache();
        }
        
        // Schedule scorecard data update in background only on final batch
        if (($processed_count == $limit) == false && $processed_count > 0 && empty($errors)) {
            if (!wp_next_scheduled('aege_update_scorecard_data_hook')) {
                wp_schedule_single_event(time() + (5 * MINUTE_IN_SECONDS), 'aege_update_scorecard_data_hook');
            }
        }
        
        // Check if there are more posts to process by querying for one more post than the limit
        $has_more = false;
        if ($processed_count == $limit) {
            // Query for one more post to see if there are actually more posts
            $next_batch_args = array(
                'post_type' => $enabled_post_types,
                'posts_per_page' => 1,
                'offset' => $offset + $limit,
                'post_status' => 'publish',
                'meta_query' => array(
                    array(
                        'key' => '_aege_enabled',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            );
            
            $next_batch_query = new WP_Query($next_batch_args);
            $has_more = $next_batch_query->have_posts();
            wp_reset_postdata();
        }
        
        AEGE_Logger::info('Batch AEGE metadata update completed', array(
            'processed_count' => $processed_count,
            'errors' => count($errors),
            'has_more' => $has_more
        ));
        
        return array(
            'success' => true,
            'processed' => $processed_count,
            'errors' => $errors,
            'has_more' => $has_more
        );
    }
    
    /**
     * Get total count of AEGE-enabled posts for progress tracking
     */
    public function get_aege_enabled_post_count() {
        global $wpdb;
        $options = get_option('aege_settings');
        $enabled_post_types = isset($options['post_types']) ? array_keys($options['post_types']) : array('post');
        
        if (empty($enabled_post_types)) {
            return 0;
        }
        
        // Create a placeholder string like '%s, %s, %s'
        $post_types_placeholder = implode(', ', array_fill(0, count($enabled_post_types), '%s'));

        // Use $wpdb->prepare to safely insert the values (spread array with ... operator)
        $sql = $wpdb->prepare("
            SELECT COUNT(p.ID)
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type IN ($post_types_placeholder)
            AND p.post_status = 'publish'
            AND pm.meta_key = '_aege_enabled'
            AND pm.meta_value = '1'
        ", ...$enabled_post_types); // Spread the array of values
        
        return (int) $wpdb->get_var($sql);
    }
    
    /**
     * Get content for llms.txt file
     */
    public function get_llms_file_content() {
        // Format as llms.txt content
        $content = "# LLM Content Discovery File for AEGE Optimizer\n";
        $content .= "# Generated: " . date('Y-m-d H:i:s') . "\n";
        $content .= "# This file helps LLMs discover your optimized content\n\n";
        
        // Add file format version
        $content .= "# Format: URL | Content Type | Priority | Last Updated | Categories\n";
        $content .= "# Priority: 1=High, 2=Medium, 3=Low\n\n";
        
        // Get all AEGE-enabled content with additional metadata
        $content_items = $this->get_enhanced_llms_content();
        
        // Group content by priority
        $high_priority = array();
        $medium_priority = array();
        $low_priority = array();
        
        foreach ($content_items as $item) {
            switch ($item['priority']) {
                case 1:
                    $high_priority[] = $item;
                    break;
                case 2:
                    $medium_priority[] = $item;
                    break;
                case 3:
                default:
                    $low_priority[] = $item;
                    break;
            }
        }
        
        // Add high priority content first
        if (!empty($high_priority)) {
            $content .= "# High Priority Content (1)\n";
            foreach ($high_priority as $item) {
                $content .= $item['url'] . " | " . $item['type'] . " | " . $item['priority'] . " | " . $item['updated'] . " | " . $item['categories'] . "\n";
            }
            $content .= "\n";
        }
        
        // Add medium priority content
        if (!empty($medium_priority)) {
            $content .= "# Medium Priority Content (2)\n";
            foreach ($medium_priority as $item) {
                $content .= $item['url'] . " | " . $item['type'] . " | " . $item['priority'] . " | " . $item['updated'] . " | " . $item['categories'] . "\n";
            }
            $content .= "\n";
        }
        
        // Add low priority content
        if (!empty($low_priority)) {
            $content .= "# Low Priority Content (3)\n";
            foreach ($low_priority as $item) {
                $content .= $item['url'] . " | " . $item['type'] . " | " . $item['priority'] . " | " . $item['updated'] . " | " . $item['categories'] . "\n";
            }
            $content .= "\n";
        }
        
        return $content;
    }
    
    /**
     * Get enhanced AEGE content with metadata for llms.txt
     */
    private function get_enhanced_llms_content() {
        $items = array();
        
        // Check the workflow setting
        $workflow = isset($this->options['workflow']) ? $this->options['workflow'] : 'automated_override';
        
        $args = array(
            'post_type' => array_keys($this->options['post_types']),
            'posts_per_page' => -1,
            'post_status' => 'publish'
        );
        
        // If not using automated workflow, only include explicitly enabled posts
        if ($workflow !== 'automated_override') {
            $args['meta_query'] = array(
                array(
                    'key' => '_aege_enabled',
                    'value' => '1',
                    'compare' => '='
                )
            );
        }
        
        $query = new WP_Query($args);
        
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();
                $post = get_post($post_id);
                
                // Check if AEGE is enabled for this post (for non-automated workflows)
                if ($workflow !== 'automated_override') {
                    $aege_enabled = get_post_meta($post_id, '_aege_enabled', true);
                    if (!$aege_enabled) {
                        continue;
                    }
                }
                
                // Get post type
                $post_type = get_post_type_object($post->post_type);
                $type = $post_type ? $post_type->labels->singular_name : $post->post_type;
                
                // Determine priority based on various factors
                $priority = $this->calculate_content_priority($post);
                
                // Get categories
                $categories = wp_get_post_categories($post_id, array('fields' => 'names'));
                $category_list = !empty($categories) ? implode(',', $categories) : 'uncategorized';
                
                // Get last updated time
                $last_updated = get_the_modified_date('Y-m-d');
                
                $items[] = array(
                    'url' => trailingslashit(get_permalink($post_id)) . 'llm/',
                    'type' => $type,
                    'priority' => $priority,
                    'updated' => $last_updated,
                    'categories' => $category_list
                );
            }
        }
        
        wp_reset_postdata();
        
        return $items;
    }
    
    /**
     * Calculate content priority based on various factors
     */
    private function calculate_content_priority($post) {
        $priority = 3; // Default to low priority
        
        // Factor 1: Post type (pages might be more important than posts)
        if ($post->post_type === 'page') {
            $priority = min($priority, 2);
        }
        
        // Factor 2: Recent updates (updated in last 30 days)
        $modified_time = strtotime($post->post_modified);
        $thirty_days_ago = time() - (30 * 24 * 60 * 60);
        if ($modified_time > $thirty_days_ago) {
            $priority = min($priority, 1);
        }
        
        // Factor 3: Comment count (more engagement = higher priority)
        if ($post->comment_count > 10) {
            $priority = min($priority, 2);
        } else if ($post->comment_count > 0) {
            $priority = min($priority, 3);
        }
        
        // Factor 4: Content length (longer content might be more valuable)
        $word_count = str_word_count(strip_tags($post->post_content));
        if ($word_count > 1000) {
            $priority = min($priority, 2);
        }
        
        return $priority;
    }
    
    /**
     * Handle AJAX request to create llms.txt file
     */
    public function handle_create_llms_file() {
        // Check permissions (nonce already verified in aege.php)
        if (!current_user_can('manage_options')) {
            AEGE_Logger::warning('Insufficient permissions for llms.txt creation via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        // Get llms.txt content
        $content = $this->get_llms_file_content();
        
        // Try to write to root directory
        $upload_dir = ABSPATH;
        $file_path = $upload_dir . 'llms.txt';
        
        // Write file
        $result = file_put_contents($file_path, $content);
        
        if ($result !== false) {
            AEGE_Logger::info('llms.txt file created successfully via AJAX', array(
                'user_id' => get_current_user_id(),
                'file_path' => $file_path,
                'file_size' => $result
            ));
            wp_send_json_success(array('message' => 'llms.txt file created successfully!'));
        } else {
            AEGE_Logger::error('Failed to create llms.txt file via AJAX', array(
                'user_id' => get_current_user_id(),
                'file_path' => $file_path
            ));
            wp_send_json_error(array('message' => 'Failed to create llms.txt file. Please check file permissions.'));
        }
    }
    
    /**
     * Handle AJAX request for schema detection
     */
    public function handle_schema_detection() {
        // Check permissions (nonce already verified in aege.php)
        if (!current_user_can('edit_posts')) {
            AEGE_Logger::warning('Insufficient permissions for schema detection via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        // Get content from request or post
        $content = isset($_POST['content']) ? wp_kses_post($_POST['content']) : '';
        
        // If no content provided in request, try to get it from post
        if (empty($content)) {
            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            if (!empty($post_id)) {
                $post = get_post($post_id);
                if ($post) {
                    // Apply the_content filter to process shortcodes etc.
                    $content = apply_filters('the_content', $post->post_content);
                    // Load the content cleaner
                    if (!class_exists('AEGE_Content_Cleaner')) {
                        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
                    }
                    $content_cleaner = new AEGE_Content_Cleaner();
                    // Clean the content using the standard AEGE cleaning process
                    $content = $content_cleaner->clean($content);
                }
            }
        }
        
        if (empty($content)) {
            AEGE_Logger::warning('No content provided for schema detection', array(
                'user_id' => get_current_user_id()
            ));
            wp_send_json_error(array('message' => 'No content provided'));
        }
        
        // Simple schema detection logic
        $detectedSchemas = array();
        
        // Detect HowTo patterns
        if (preg_match('/how to/i', $content) || preg_match('/\bstep\b/i', $content) || preg_match('/\d+\.\s/i', $content)) {
            $detectedSchemas[] = 'HowTo';
        }
        
        // Detect FAQ patterns
        if (preg_match_all('/\?/', $content, $matches) && count($matches[0]) >= 2) {
            $detectedSchemas[] = 'FAQPage';
        }
        
        // Detect Product patterns
        if (preg_match('/\$[\d,]+\.?\d*/i', $content) || preg_match('/\bprice\b/i', $content) || preg_match('/\brating\b/i', $content)) {
            $detectedSchemas[] = 'Product';
        }
        
        // Detect Review patterns
        if (preg_match('/\b(review|rating|stars)\b/i', $content) && 
            (preg_match('/\b(out of|\/|of)\s*\d+\b/', $content) || 
             preg_match('/★/', $content))) {
            $detectedSchemas[] = 'Review';
        }
        
        // Detect Recipe patterns
        if (preg_match('/<h[1-6][^>]*>(.*?[Rr]ecipe.*?)<\/h[1-6]>/s', $content) ||
            preg_match('/\b(ingredients|prep time|cook time|servings)\b/i', $content)) {
            $detectedSchemas[] = 'Recipe';
        }
        
        // Detect Event patterns
        if (preg_match('/\b(event|concert|conference|webinar|workshop)\b/i', $content) &&
            (preg_match('/\b(\d{4}-\d{2}-\d{2}|\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b)/', $content) ||
             preg_match('/\b(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\b/', $content))) {
            $detectedSchemas[] = 'Event';
        }
        
        AEGE_Logger::info('Schema detection completed', array(
            'user_id' => get_current_user_id(),
            'detected_schemas' => $detectedSchemas,
            'content_length' => strlen($content)
        ));
        
        wp_send_json_success(array('schemas' => $detectedSchemas));
    }
    
    /**
     * Handle AJAX request for copying original content
     */
    public function handle_copy_original_content() {
        // Check permissions (nonce already verified in aege.php)
        if (!current_user_can('edit_posts')) {
            AEGE_Logger::warning('Insufficient permissions for copying original content via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        // Get post ID from request
        $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
        
        if (empty($post_id)) {
            AEGE_Logger::warning('No post ID provided for copying original content', array(
                'user_id' => get_current_user_id()
            ));
            wp_send_json_error(array('message' => 'No post ID provided'));
        }
        
        // Get the post
        $post = get_post($post_id);
        
        if (!$post) {
            AEGE_Logger::error('Post not found for copying original content', array(
                'user_id' => get_current_user_id(),
                'post_id' => $post_id
            ));
            wp_send_json_error(array('message' => 'Post not found'));
        }
        
        // Apply the_content filter to process shortcodes etc.
        $content = apply_filters('the_content', $post->post_content);
        
        // Load the content cleaner
        if (!class_exists('AEGE_Content_Cleaner')) {
            require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
        }
        
        $content_cleaner = new AEGE_Content_Cleaner();
        
        // Clean the content using the standard AEGE cleaning process
        $cleaned_content = $content_cleaner->clean($content);
        
        AEGE_Logger::info('Original content copied and cleaned successfully', array(
            'user_id' => get_current_user_id(),
            'post_id' => $post_id,
            'original_length' => strlen($post->post_content),
            'cleaned_length' => strlen($cleaned_content)
        ));
        
        wp_send_json_success(array('content' => $cleaned_content));
    }
    
    /**
     * Unified AJAX handler for batch metadata update
     */
    public function handle_batch_update_metadata() {
        // Check nonce using modern WordPress method
        check_ajax_referer('aege_dashboard_nonce', 'nonce');
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            AEGE_Logger::warning('Insufficient permissions for batch metadata update via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        // Get batch parameters
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;
        
        // Validate limits to prevent abuse
        $limit = max(10, min(100, $limit)); // Between 10-100 posts per batch
        
        // Perform batch update
        $result = $this->bulk_update_aege_metadata($offset, $limit);
        
        if (isset($result['success']) && $result['success']) {
            // Schedule scorecard data update in background when bulk update is complete
            if (isset($result['has_more']) && $result['has_more'] == false) {
                if (!wp_next_scheduled('aege_update_scorecard_data_hook')) {
                    wp_schedule_single_event(time() + (5 * MINUTE_IN_SECONDS), 'aege_update_scorecard_data_hook');
                }
            }
            
            wp_send_json_success(array(
                'message' => 'Processed ' . $result['processed'] . ' posts.',
                'processed' => $result['processed'],
                'has_more' => isset($result['has_more']) ? $result['has_more'] : false,
                'next_offset' => $offset + $limit,
                'limit' => $limit,
                'errors' => isset($result['errors']) ? $result['errors'] : array()
            ));
        } else {
            wp_send_json_error(array('message' => 'Failed to update metadata.'));
        }
    }
    
    /**
     * Unified AJAX handler for getting post count
     */
    public function handle_get_post_count() {
        check_ajax_referer('aege_dashboard_nonce', 'nonce'); // More modern way to check nonces

        if (!current_user_can('manage_options')) {
            AEGE_Logger::warning('Insufficient permissions for getting post count via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        $total_count = $this->get_aege_enabled_post_count();
        wp_send_json_success(array('total' => $total_count));
    }
    
    /**
     * Unified AJAX handler for creating llms.txt file
     */
    public function handle_create_llms_file_ajax() {
        check_ajax_referer('aege_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            AEGE_Logger::warning('Insufficient permissions for llms.txt creation via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        $this->handle_create_llms_file();
    }
    
    /**
     * Unified AJAX handler for schema detection
     */
    public function handle_schema_detection_ajax() {
        check_ajax_referer('aege_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            AEGE_Logger::warning('Insufficient permissions for schema detection via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        $this->handle_schema_detection();
    }
    
    /**
     * Unified AJAX handler for copying original content
     */
    public function handle_copy_original_content_ajax() {
        check_ajax_referer('aege_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            AEGE_Logger::warning('Insufficient permissions for copying original content via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        $this->handle_copy_original_content();
    }
    
    /**
     * Unified AJAX handler for publishing LLM content
     */
    public function handle_publish_llm_ajax() {
        check_ajax_referer('aege_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            AEGE_Logger::warning('Insufficient permissions for publishing LLM content via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        $this->handle_publish_llm();
    }
    
    /** 
     * Handle AJAX request for publishing LLM content
     */
    public function handle_publish_llm() {
        // Check permissions (nonce already verified in aege.php)
        if (!current_user_can('edit_posts')) {
            AEGE_Logger::warning('Insufficient permissions for creating/updating LLM content via AJAX', array(
                'user_id' => get_current_user_id(),
                'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
            ));
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }
        
        // Get post ID from request
        $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
        
        if (empty($post_id)) {
            AEGE_Logger::warning('No post ID provided for creating/updating LLM content', array(
                'user_id' => get_current_user_id()
            ));
            wp_send_json_error(array('message' => 'No post ID provided'));
        }
        
        // Get the post
        $post = get_post($post_id);
        
        if (!$post) {
            AEGE_Logger::error('Post not found for creating/updating LLM content', array(
                'user_id' => get_current_user_id(),
                'post_id' => $post_id
            ));
            wp_send_json_error(array('message' => 'Post not found'));
        }
        
        // Check if post is published
        if ($post->post_status !== 'publish') {
            AEGE_Logger::warning('Cannot create/update LLM content for unpublished post', array(
                'user_id' => get_current_user_id(),
                'post_id' => $post_id,
                'post_status' => $post->post_status
            ));
            wp_send_json_error(array('message' => 'LLM content can only be created/updated for published posts'));
        }
        
        // Get content from AJAX request
        $content = isset($_POST['content']) ? wp_kses_post($_POST['content']) : '';
        
        if (empty($content)) {
            AEGE_Logger::warning('No content provided for creating/updating LLM content', array(
                'user_id' => get_current_user_id(),
                'post_id' => $post_id
            ));
            wp_send_json_error(array('message' => 'No content provided'));
        }
        
        // Process content through content cleaner to ensure citations are properly marked
        if (class_exists('AEGE_Content_Cleaner')) {
            $content_cleaner = new AEGE_Content_Cleaner();
            $processed_content = $content_cleaner->clean($content);
        } else {
            // Fallback to basic content cleaning
            $processed_content = wp_kses_post($content);
        }
        
        // Save the content as custom AEGE content
        update_post_meta($post_id, '_aege_custom_content', $processed_content);
        AEGE_Logger::info('LLM content created/updated successfully from editor content', array(
            'user_id' => get_current_user_id(),
            'post_id' => $post_id,
            'content_length' => strlen($processed_content)
        ));
        
        // Calculate and save citation count on the processed content
        $citation_count = $this->calculate_citation_count($processed_content);
        update_post_meta($post_id, '_aege_citation_count', $citation_count);
        
        // Detect and save schemas
        if (!class_exists('AEGE_Content_Cleaner')) {
            require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
        }
        $content_cleaner = new AEGE_Content_Cleaner();
        $schemas = $content_cleaner->detect_and_generate_schemas($content, $post_id);
        update_post_meta($post_id, '_aege_schemas', $schemas);
        
        // Update the AEO freshness timestamp with current time
        update_post_meta($post_id, '_aege_llm_last_updated', time());
        
        // Clear the AEGE cache for this post to force regeneration with new content
        if (class_exists('AEGE_Cache_Manager')) {
            AEGE_Cache_Manager::clear_cache_for_object($post_id, 'post');
            AEGE_Logger::info('AEGE cache cleared for post after LLM content update', array(
                'user_id' => get_current_user_id(),
                'post_id' => $post_id
            ));
        }
        
        // Clear dashboard cache
        $this->clear_dashboard_cache();
        
        // Schedule llms.txt file regeneration with debounce
        if ( ! wp_next_scheduled('aege_schedule_llms_regeneration') ) {
            wp_schedule_single_event(time() + (5 * MINUTE_IN_SECONDS), 'aege_schedule_llms_regeneration');
        }
        
        AEGE_Logger::info('LLM content created/updated successfully', array(
            'user_id' => get_current_user_id(),
            'post_id' => $post_id
        ));
        
        wp_send_json_success(array('message' => 'LLM content created/updated successfully'));
    }
    
    /**
     * Regenerate llms.txt file
     */
    public function regenerate_llms_file() {
        // Only regenerate if AEGE is active
        $options = get_option('aege_settings');
        if (empty($options['master_switch'])) {
            AEGE_Logger::debug('Skipping llms.txt regeneration - AEGE is not active');
            return false;
        }
        
        // Get llms.txt content
        $content = $this->get_llms_file_content();
        
        // Try to write to root directory
        $file_path = ABSPATH . 'llms.txt';
        
        // Write file
        $result = file_put_contents($file_path, $content);
        
        if ($result !== false) {
            AEGE_Logger::info('llms.txt file regenerated successfully', array(
                'file_path' => $file_path,
                'file_size' => $result
            ));
            return true;
        } else {
            AEGE_Logger::error('Failed to regenerate llms.txt file', array(
                'file_path' => $file_path
            ));
            return false;
        }
    }
    
    /**
     * Render API documentation section
     */
    private function render_api_documentation() {
        ?>
        <div class="aege-api-docs" style="margin-top: 40px; padding: 20px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 5px;">
            <h2><?php esc_html_e( 'AEGE REST API Documentation', 'aege' ); ?></h2>
            <p><?php esc_html_e( 'AEGE provides REST API endpoints for external LLM integration using WordPress Application Passwords for authentication.', 'aege' ); ?></p>
            
            <h3><?php esc_html_e( 'Authentication', 'aege' ); ?></h3>
            <p><?php esc_html_e( 'To use the AEGE REST API, you need to create an Application Password in your WordPress admin area:', 'aege' ); ?></p>
            <ol>
                <li><?php esc_html_e( 'Go to Users → Your Profile', 'aege' ); ?></li>
                <li><?php esc_html_e( 'Scroll down to "Application Passwords" section', 'aege' ); ?></li>
                <li><?php esc_html_e( 'Enter "AEGE LLM Integration" as the app name', 'aege' ); ?></li>
                <li><?php esc_html_e( 'Click "Add New Application Password"', 'aege' ); ?></li>
                <li><?php esc_html_e( 'Copy the generated password (you won\'t see it again!)', 'aege' ); ?></li>
            </ol>
            <p><?php esc_html_e( 'Use Basic Authentication with your WordPress username and the Application Password:', 'aege' ); ?></p>
            <pre>Authorization: Basic base64_encode(username:application_password)</pre>
            
            <h3><?php esc_html_e( 'Available Endpoints', 'aege' ); ?></h3>
            
            <h4><?php esc_html_e( 'Get Content', 'aege' ); ?></h4>
            <p><code>GET /wp-json/aege/v1/content/{post_id}</code></p>
            <p><?php esc_html_e( 'Retrieve AEGE-optimized content for a specific post.', 'aege' ); ?></p>
            <p><strong><?php esc_html_e( 'Response:', 'aege' ); ?></strong></p>
            <pre>{
  "id": 123,
  "title": "Post Title",
  "content": {
    "auto_generated": "...",
    "custom": "...",
    "active": "..."
  },
  "schema": {...},
  "meta": {...},
  "aege_meta": {...}
}</pre>
            
            <h4><?php esc_html_e( 'Update Content', 'aege' ); ?></h4>
            <p><code>POST /wp-json/aege/v1/content/{post_id}</code></p>
            <p><?php esc_html_e( 'Update custom AEGE content for a specific post.', 'aege' ); ?></p>
            <p><strong><?php esc_html_e( 'Parameters:', 'aege' ); ?></strong></p>
            <ul>
                <li><code>content</code> (string, optional) - <?php esc_html_e( 'Custom AEGE content', 'aege' ); ?></li>
                <li><code>title</code> (string, optional) - <?php esc_html_e( 'Post title', 'aege' ); ?></li>
                <li><code>meta_description</code> (string, optional) - <?php esc_html_e( 'Meta description', 'aege' ); ?></li>
            </ul>
            
            <h4><?php esc_html_e( 'List Content', 'aege' ); ?></h4>
            <p><code>GET /wp-json/aege/v1/content</code></p>
            <p><?php esc_html_e( 'Get a list of AEGE-enabled content.', 'aege' ); ?></p>
            <p><strong><?php esc_html_e( 'Parameters:', 'aege' ); ?></strong></p>
            <ul>
                <li><code>post_type</code> (string, optional, default: post) - <?php esc_html_e( 'Post type to filter by', 'aege' ); ?></li>
                <li><code>per_page</code> (int, optional, default: 10) - <?php esc_html_e( 'Number of items per page', 'aege' ); ?></li>
                <li><code>page</code> (int, optional, default: 1) - <?php esc_html_e( 'Page number', 'aege' ); ?></li>
            </ul>
            
            <h4><?php esc_html_e( 'Regenerate Content', 'aege' ); ?></h4>
            <p><code>POST /wp-json/aege/v1/content/{post_id}/regenerate</code></p>
            <p><?php esc_html_e( 'Force regeneration of AEGE content for a specific post.', 'aege' ); ?></p>
            
            <h4><?php esc_html_e( 'Get Categories', 'aege' ); ?></h4>
            <p><code>GET /wp-json/aege/v1/categories</code></p>
            <p><?php esc_html_e( 'Get all categories with AEGE LLM URLs.', 'aege' ); ?></p>
            
            <h4><?php esc_html_e( 'Get Tags', 'aege' ); ?></h4>
            <p><code>GET /wp-json/aege/v1/tags</code></p>
            <p><?php esc_html_e( 'Get all tags with AEGE LLM URLs.', 'aege' ); ?></p>
            
            <h4><?php esc_html_e( 'Get Sitemap', 'aege' ); ?></h4>
            <p><code>GET /wp-json/aege/v1/sitemap</code></p>
            <p><?php esc_html_e( 'Get AEGE sitemap data for all enabled content types.', 'aege' ); ?></p>
            
            <h3><?php esc_html_e( 'Example Usage', 'aege' ); ?></h3>
            <p><?php esc_html_e( 'Using cURL:', 'aege' ); ?></p>
            <pre>curl -u username:application_password \
  https://yoursite.com/wp-json/aege/v1/content/123</pre>
        </div>
        <?php
    }
    
    /**
     * Analyze content freshness
     *
     * @return array Freshness statistics
     */
    private function analyze_content_freshness() {
        global $wpdb;
        $stats = array(
            'updated_30_days' => 0,
            'updated_90_days' => 0,
            'not_updated_1_year' => 0,
            'update_timeline' => array()
        );
        
        // Fast, direct SQL query for our pre-calculated timestamps
        $timestamps = $wpdb->get_col("
            SELECT meta_value FROM $wpdb->postmeta 
            WHERE meta_key = '_aege_llm_last_updated'
        ");
        
        $thirty_days_ago = strtotime('-30 days');
        $ninety_days_ago = strtotime('-90 days');
        $one_year_ago = strtotime('-1 year');
        
        foreach ($timestamps as $timestamp) {
            if ($timestamp > $thirty_days_ago) {
                $stats['updated_30_days']++;
            }
            
            if ($timestamp > $ninety_days_ago) {
                $stats['updated_90_days']++;
            }
            
            if ($timestamp < $one_year_ago) {
                $stats['not_updated_1_year']++;
            }
            
            // Group by month for timeline
            $month = date('Y-m', $timestamp);
            if (!isset($stats['update_timeline'][$month])) {
                $stats['update_timeline'][$month] = 0;
            }
            $stats['update_timeline'][$month]++;
        }
        
        // Sort timeline chronologically
        ksort($stats['update_timeline']);
        
        return $stats;
    }
    
    
    
    /**
     * Analyze schema coverage
     *
     * @return array Schema statistics
     */
    private function analyze_schema_coverage() {
        global $wpdb;
        $stats = array(
            'faq' => 0,
            'howto' => 0,
            'product' => 0,
            'review' => 0,
            'recipe' => 0,
            'event' => 0,
            'total_with_schema' => 0
        );
        
        // Fast, direct query for pre-calculated schemas
        $schema_results = $wpdb->get_results("
            SELECT meta_value FROM $wpdb->postmeta
            WHERE meta_key = '_aege_schemas'
        ");
        
        foreach ($schema_results as $row) {
            $schemas = maybe_unserialize($row->meta_value);
            if (is_array($schemas) && !empty($schemas)) {
                $stats['total_with_schema']++;
                
                foreach ($schemas as $schema) {
                    if (!is_array($schema) || !isset($schema['@type'])) {
                        continue;
                    }
                    
                    switch ($schema['@type']) {
                        case 'FAQPage':
                            $stats['faq']++;
                            break;
                        case 'HowTo':
                            $stats['howto']++;
                            break;
                        case 'Product':
                            $stats['product']++;
                            break;
                        case 'Review':
                            $stats['review']++;
                            break;
                        case 'Recipe':
                            $stats['recipe']++;
                            break;
                        case 'Event':
                            $stats['event']++;
                            break;
                    }
                }
            }
        }
        
        return $stats;
    }
    
    /**
     * Adds the custom AEO Score column to the posts list table.
     *
     * @param array $columns The existing columns.
     * @return array The modified columns.
     */
    public function add_aege_columns($columns) {
        // CORRECT: Return only the content for the header. WordPress will build the link.
        $aege_column_header = '<span class="aege-header-text">llm</span>';
        
        $aege_column = ['aege_score' => $aege_column_header];

        // This positioning logic is correct and should be kept.
        $date_column_offset = array_search('date', array_keys($columns));
        if ($date_column_offset !== false) {
            return array_slice($columns, 0, $date_column_offset + 1, true) +
                   $aege_column +
                   array_slice($columns, $date_column_offset + 1, null, true);
        }

        $columns['aege_score'] = $aege_column['aege_score'];
        return $columns;
    }
    
    /**
     * Renders the content for the custom AEO Score column.
     *
     * @param string $column_name The name of the column being rendered.
     * @param int    $post_id     The ID of the current post.
     */
    public function render_aege_columns($column_name, $post_id) {
        if ($column_name === 'aege_score') {
            $score = get_post_meta($post_id, '_aege_aeo_score', true);
            
            if (is_numeric($score)) {
                $score = round($score);
                $formatted_score = sprintf('%d%%', $score);
                $scorecard_url = admin_url('admin.php?page=aege-analysis#post-' . $post_id);

                // Determine the color-coding class based on the score.
                $score_class = 'aege-score-neutral';
                if ($score >= 75) {
                    $score_class = 'aege-score-good';
                } elseif ($score < 50) {
                    $score_class = 'aege-score-poor';
                }

                printf(
                    '<a href="%s" class="%s" style="font-weight: bold;">%s</a>',
                    esc_url($scorecard_url),
                    esc_attr($score_class),
                    esc_html($formatted_score)
                );
            } else {
                // Display an em dash if the score is not available.
                echo '—';
            }
        }
    }
    
    /**
     * Makes the 'llm' column sortable.
     *
     * @param array $columns The existing sortable columns.
     * @return array The modified sortable columns.
     */
    public function make_aege_columns_sortable($columns) {
        $columns['aege_score'] = '_aege_aeo_score';
        return $columns;
    }
    
    /**
     * Handles the sorting logic when a user clicks on a custom column header.
     *
     * @param WP_Query $query The main WordPress query object.
     */
    public function handle_aege_column_sorting($query) {
        // We only modify the main query on admin screens.
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }

        $orderby = $query->get('orderby');

        // If WordPress is trying to sort by one of our columns, we tell it how.
        $sortable_keys = [
            '_aege_aeo_score',
            '_aege_citation_count',
            '_aege_entity_count',
            '_aege_llm_last_updated',
        ];

        if (in_array($orderby, $sortable_keys)) {
            $query->set('meta_key', $orderby);
            // Crucially, we tell WordPress to sort these values as numbers.
            $query->set('orderby', 'meta_value_num');
        }
    }
    
    /**
     * Adds a "View LLM Version" link to the post row actions.
     *
     * @param array   $actions The existing row actions.
     * @param WP_Post $post    The current post object.
     * @return array The modified actions.
     */
    public function add_aege_row_actions($actions, $post) {
        // Only add the link if the post is AEGE-enabled.
        if (get_post_meta($post->ID, '_aege_enabled', true) === '1') {
            $llm_url = get_permalink($post->ID) . 'llm/';
            $actions['aege_preview'] = sprintf(
                '<a href="%s" target="_blank" rel="noopener">View LLM Version</a>',
                esc_url($llm_url)
            );
        }
        return $actions;
    }
    
    /**
     * Adds the necessary CSS to the admin header for the tooltip and column styling.
     */
    public function add_aege_column_styles() {
        echo '<style>
            /*
             * FINAL, CORRECTED, RESPONSIVE & ALIGNED SOLUTION
             */

            /* --- DESKTOP STYLES (for screens wider than 782px) --- */
            @media screen and (min-width: 783px) {
                .wp-list-table th.column-aege_score {
                    width: 75px;
                }

                /* Target the link that WordPress generates in the header */
                .wp-list-table th.column-aege_score a {
                    display: flex !important;
                    justify-content: center;
                    align-items: center;
                    gap: 5px;
                }

                /* THIS IS THE NEW RULE: Center the score text in the data cells */
                .wp-list-table td.column-aege_score {
                    text-align: center !important;
                }
            }

            /* --- SHARED STYLES (Apply to all screen sizes) --- */

            /* Style the link in the header to be a neutral gray */
            .wp-list-table th.column-aege_score a {
                color: #50575e;
                text-decoration: none;
            }
            
            /* Ensure the text does not change color on hover */
            .wp-list-table th.column-aege_score a:hover {
                color: #50575e;
            }

            /* Style for the "llm" text in the header */
            .aege-header-text {
                font-size: 13px;
                font-weight: 600;
            }

            /* --- Score Color Styles (for the column body) --- */
            .aege-score-good { color: #227122 !important; }
            .aege-score-okay { color: #f59e0b !important; }
            .aege-score-poor { color: #a01313 !important; }
        </style>';
    }
    
    /**
     * Get AEGE statistics from option storage
     *
     * @return array Statistics
     */
    private function get_cached_aege_statistics() {
        // Get statistics from option storage (calculated in background)
        $stats = get_option('aege_scorecard_data', array());
        
        // If no data exists, return default structure
        if (empty($stats)) {
            return array(
                'total_content' => 0,
                'aege_enabled' => 0,
                'aege_disabled' => 0,
                'schema_coverage' => array(
                    'faq' => 0,
                    'howto' => 0,
                    'product' => 0,
                    'review' => 0,
                    'recipe' => 0,
                    'event' => 0,
                    'total_with_schema' => 0
                ),
                'citation_density' => array(
                    'total_citations' => 0,
                    'pages_with_citations' => 0,
                    'avg_citations_per_page' => 0,
                    'citation_distribution' => array()
                ),
                'content_freshness' => array(
                    'updated_30_days' => 0,
                    'updated_90_days' => 0,
                    'not_updated_1_year' => 0,
                    'update_timeline' => array()
                ),
                'entity_analysis' => array(
                    'total_entities' => 0,
                    'avg_entities_per_page' => 0,
                    'pages_with_entities' => 0
                ),
                'overall_aeo_score' => array(
                    'average_score' => 0,
                    'highest_score' => 0,
                    'lowest_score' => 0,
                    'score_distribution' => array()
                )
            );
        }
        
        return $stats;
    }
    
    /**
     * Calculate and update scorecard data in background
     * This method is called via wp_schedule_single_event to recalculate all dashboard statistics
     */
    public function update_scorecard_data_in_background() {
        AEGE_Logger::info('Starting background scorecard data update');
        
        $stats = array();
        $stats['total_content'] = $this->get_total_content_count();
        $stats['aege_enabled'] = $this->get_aege_enabled_count();
        $stats['aege_disabled'] = $stats['total_content'] - $stats['aege_enabled'];
        $stats['schema_coverage'] = $this->analyze_schema_coverage();
        $stats['citation_density'] = (new AEGE_Citation_Analyzer())->analyze_citation_density();
        $stats['content_freshness'] = $this->analyze_content_freshness();
        
        // Initialize new statistics with default values if they don't exist
        $stats['entity_analysis'] = $this->analyze_entity_density();
        $stats['overall_aeo_score'] = $this->calculate_overall_aeo_score();
        
        // Save the calculated data using update_option
        update_option('aege_scorecard_data', $stats, false);
        
        AEGE_Logger::info('Background scorecard data update completed', array(
            'aege_enabled_count' => $stats['aege_enabled']
        ));
    }
    
    /**
     * Analyze entity density across AEGE-enabled content
     *
     * @return array Entity analysis statistics
     */
    private function analyze_entity_density() {
        global $wpdb;
        
        $stats = array(
            'total_entities' => 0,
            'avg_entities_per_page' => 0,
            'pages_with_entities' => 0
        );
        
        // Check if the postmeta table exists and is accessible
        if (!class_exists('wpdb') || !$wpdb) {
            return $stats;
        }
        
        // Fast, direct query for pre-calculated entity counts
        $counts = $wpdb->get_col("
            SELECT meta_value FROM $wpdb->postmeta 
            WHERE meta_key = '_aege_entity_count'
        ");
        
        // If no results, return default stats
        if (empty($counts)) {
            return $stats;
        }
        
        $pages_with_entities = 0;
        $total_entities = 0;
        
        foreach ($counts as $count) {
            $count = (int)$count;
            $total_entities += $count;
            if ($count > 0) {
                $pages_with_entities++;
            }
        }
        
        $stats['total_entities'] = $total_entities;
        $stats['pages_with_entities'] = $pages_with_entities;
        
        // Calculate average entities per page
        $total_pages = count($counts);
        $stats['avg_entities_per_page'] = $total_pages > 0 ? 
            round($total_entities / $total_pages, 2) : 0;
            
        return $stats;
    }
    
    /**
     * Calculate overall AEO score across all AEGE-enabled content
     *
     * @return array Overall AEO score statistics
     */
    private function calculate_overall_aeo_score() {
        global $wpdb;
        
        $stats = array(
            'average_score' => 0,
            'highest_score' => 0,
            'lowest_score' => 0,
            'score_distribution' => array()
        );
        
        // Check if the postmeta table exists and is accessible
        if (!class_exists('wpdb') || !$wpdb) {
            return $stats;
        }
        
        // Fast, direct query for pre-calculated AEO scores
        $scores = $wpdb->get_col("
            SELECT meta_value FROM $wpdb->postmeta 
            WHERE meta_key = '_aege_aeo_score'
        ");
        
        // If no results, return default stats
        if (empty($scores)) {
            return $stats;
        }
        
        $total_score = 0;
        $count = count($scores);
        
        // Initialize highest and lowest with first score
        if ($count > 0) {
            $stats['highest_score'] = (int)$scores[0];
            $stats['lowest_score'] = (int)$scores[0];
        }
        
        foreach ($scores as $score) {
            $score = (int)$score;
            $total_score += $score;
            
            // Track highest and lowest scores
            $stats['highest_score'] = max($stats['highest_score'], $score);
            $stats['lowest_score'] = min($stats['lowest_score'], $score);
            
            // Track score distribution (group by 10s)
            $range = floor($score / 10) * 10;
            $range_key = $range . '-' . ($range + 9);
            if (!isset($stats['score_distribution'][$range_key])) {
                $stats['score_distribution'][$range_key] = 0;
            }
            $stats['score_distribution'][$range_key]++;
        }
        
        $stats['average_score'] = $count > 0 ? round($total_score / $count, 2) : 0;
        
        return $stats;
    }
    
    /**
     * Calculate AEO score for a post
     *
     * @param int $post_id The post ID
     * @return int AEO score (0-100)
     */
    public function calculate_aeo_score($post_id) {
        // Get post meta data with defaults
        $schema_count = get_post_meta($post_id, '_aege_schemas', true);
        $citation_count = get_post_meta($post_id, '_aege_citation_count', true);
        $entity_count = get_post_meta($post_id, '_aege_entity_count', true);
        $last_updated = get_post_meta($post_id, '_aege_llm_last_updated', true);
        
        // Schema Score (40 pts): (number of schemas found / 2) * 40, capped at 40
        $schema_array = is_array($schema_count) ? $schema_count : array();
        $schema_score = min(40, (count($schema_array) / 2) * 40);
        
        // Freshness Score (30 pts): Based on _aege_llm_last_updated timestamp
        $freshness_score = 0;
        if ($last_updated) {
            $update_time = is_numeric($last_updated) ? $last_updated : strtotime($last_updated);
            // Handle case where strtotime fails
            if ($update_time !== false) {
                $days_since_update = (time() - $update_time) / (24 * 60 * 60);
                
                if ($days_since_update <= 90) {
                    $freshness_score = 30; // Perfect score for recent updates
                } elseif ($days_since_update <= 365) {
                    // Scale linearly from 30 to 0 over the year
                    $freshness_score = max(0, 30 - (($days_since_update - 90) * 30 / 275));
                }
                // 0 points for content older than a year
            }
        }
        
        // Citation Score (30 pts): Based on citation count
        $citation_score = 0;
        if ($citation_count !== false) {
            $citation_count = (int)$citation_count;
            if ($citation_count >= 1 && $citation_count <= 3) {
                $citation_score = 30; // Perfect score for 1-3 citations
            } elseif ($citation_count > 3) {
                $citation_score = 25; // Slightly less for more citations to avoid rewarding spammy linking
            }
            // 0 points for no citations
        }
        
        // Calculate total score
        $total_score = $schema_score + $freshness_score + $citation_score;
        
        // Ensure score is between 0 and 100
        return max(0, min(100, round($total_score)));
    }
    
    
    
    

    public function ajax_save_llm_content() {
        check_ajax_referer('aege_llm_editor_nonce', 'security');

        if (!current_user_can('edit_post', $_POST['post_id'])) {
            wp_send_json_error(array('message' => 'Permission denied.'));
        }

        if (isset($_POST['llm_content'])) {
            update_post_meta($_POST['post_id'], '_aege_custom_content', wp_kses_post($_POST['llm_content']));
            wp_send_json_success(array('message' => 'LLM content saved.'));
        } else {
            wp_send_json_error(array('message' => 'No content provided.'));
        }
    }
}
