<?php
/**
 * Plugin Name:       AEGE
 * Plugin URI:        [Your Plugin's Website URL]
 * Description:       The definitive plugin for optimizing your content for Answer Engines and Generative Engines. Includes comprehensive test suite in the tests/ directory and detailed logging capabilities. Features AEO Score column for quick content optimization assessment.
 * Version:           1.1.5
 * Author:            [Your Name/Company]
 * Author URI:        [Your Website URL]
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       aege
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

define( 'AEGE_VERSION', '1.1.5' );
define( 'AEGE_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );

function activate_aege() {
	require_once AEGE_PLUGIN_DIR . 'includes/class-aege-activator.php';
	AEGE_Activator::activate();
}

function deactivate_aege() {
	require_once AEGE_PLUGIN_DIR . 'includes/class-aege-deactivator.php';
	AEGE_Deactivator::deactivate();
}

// Initialize cache management
add_action('plugins_loaded', function() {
    if (class_exists('AEGE_Cache_Manager')) {
        AEGE_Cache_Manager::init();
    }
});

register_activation_hook( __FILE__, 'activate_aege' );
register_deactivation_hook( __FILE__, 'deactivate_aege' );

// Load plugin text domain for internationalization
add_action('plugins_loaded', 'aege_load_textdomain');
function aege_load_textdomain() {
    load_plugin_textdomain('aege', false, dirname(plugin_basename(__FILE__)) . '/languages/');
}

// Load main plugin class
require AEGE_PLUGIN_DIR . 'includes/class-aege-main.php';

// Load standalone sitemap class
require AEGE_PLUGIN_DIR . 'includes/class-aege-standalone-sitemap.php';

// Load database cache class
require AEGE_PLUGIN_DIR . 'includes/class-aege-database-cache.php';

// Load logger class
require AEGE_PLUGIN_DIR . 'includes/class-aege-logger.php';

// Handle sitemap requests early
add_action('init', function() {
    // Handle sitemap requests early
    if (class_exists('AEGE_Standalone_Sitemap')) {
        AEGE_Standalone_Sitemap::handle_sitemap_request();
    }
}, 1);

// Handle llms.txt requests via a dedicated hook
add_action('parse_request', function($wp) {
    // Handle llms.txt requests
    if (isset($_SERVER['REQUEST_URI']) && $_SERVER['REQUEST_URI'] === '/llms.txt') {
        status_header(200);
        header('Content-Type: text/plain; charset=utf-8');
        $admin = AEGE_Admin::get_instance('aege', AEGE_VERSION);
        echo $admin->get_llms_file_content();
        exit;
    }
}, 1);

// Handle log viewing requests early
add_action('admin_init', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'aege' && 
        isset($_GET['aege_action']) && $_GET['aege_action'] === 'view_logs' && 
        current_user_can('manage_options')) {
        // Set headers for plain text output
        nocache_headers();
        header('Content-Type: text/plain; charset=' . get_option('blog_charset'));
        header('X-Robots-Tag: noindex, nofollow');
        
        // Output log content and exit immediately
        echo AEGE_Logger::get_log_content();
        exit;
    }
});



// Regenerate llms.txt when posts are saved, published, or updated
add_action('save_post', 'aege_schedule_llms_regeneration', 10, 3);
add_action('publish_post', 'aege_schedule_llms_regeneration');
add_action('publish_page', 'aege_schedule_llms_regeneration');
add_action('trashed_post', 'aege_schedule_llms_regeneration');
add_action('untrashed_post', 'aege_schedule_llms_regeneration');

// Regenerate llms.txt when AEGE settings are updated
add_action('update_option_aege_settings', 'aege_schedule_llms_regeneration', 10, 3);

/**
 * Schedule llms.txt file regeneration
 */
function aege_schedule_llms_regeneration() {
    // Check if regeneration is already scheduled
    if (!wp_next_scheduled('aege_regenerate_llms_file_cron')) {
        // Schedule regeneration with a slight delay to batch multiple changes
        wp_schedule_single_event(time() + 60, 'aege_regenerate_llms_file_cron');
    }
}

/**
 * Schedule scorecard data update
 * This function is called when posts are saved to update dashboard statistics in the background
 */
/**
 * Trigger scorecard update on post save
 * This function is called via wp_after_insert_post hook to ensure post meta is fully saved
 */
function aege_trigger_scorecard_update_on_save($post_id, $post, $update) {
    // We only care about real post types that are being updated.
    if (wp_is_post_revision($post_id) || !$update) {
        return;
    }
    
    // Schedule the background job.
    if (!wp_next_scheduled('aege_update_scorecard_data_hook')) {
        wp_schedule_single_event(time() + (5 * MINUTE_IN_SECONDS), 'aege_update_scorecard_data_hook');
    }
}
add_action('wp_after_insert_post', 'aege_trigger_scorecard_update_on_save', 10, 3);

/**
 * Regenerate llms.txt file via cron
 */
function aege_regenerate_llms_file_cron() {
    // Only regenerate if AEGE is active
    $options = get_option('aege_settings');
    if (empty($options['master_switch'])) {
        return;
    }
    
    // Check if we should regenerate based on transient to avoid too frequent updates
    $last_regeneration = get_transient('aege_llms_last_regeneration');
    if ($last_regeneration !== false) {
        // Don't regenerate if last regeneration was less than 5 minutes ago
        AEGE_Logger::debug('Skipping llms.txt regeneration - too soon', array(
            'last_regeneration' => $last_regeneration,
            'current_time' => time()
        ));
        return;
    }
    
    // Set transient to prevent too frequent regenerations
    set_transient('aege_llms_last_regeneration', time(), 300); // 5 minutes
    
    // Create admin instance and regenerate
    $admin = AEGE_Admin::get_instance('aege', AEGE_VERSION);
    $result = $admin->regenerate_llms_file();
    
    if ($result) {
        AEGE_Logger::info('llms.txt file regenerated successfully');
    } else {
        AEGE_Logger::error('Failed to regenerate llms.txt file');
    }
}
add_action('aege_regenerate_llms_file_cron', 'aege_regenerate_llms_file_cron');


/**
 * The main function that runs the plugin.
 */
function run_aege() {
    // Load dependencies
    require_once plugin_dir_path(__FILE__) . 'includes/class-aege-main.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-aege-admin.php';
    // ... other require statements ...

    // Initialize the public-facing side of the plugin.
    $plugin_main = new AEGE_Main();
    $plugin_main->run(); // This will register the public hooks.

    // Initialize the admin-facing side of the plugin.
    if (is_admin()) {
        // Use the correct static method to get the single instance of AEGE_Admin.
        $plugin_admin = AEGE_Admin::get_instance('aege', AEGE_VERSION);
        
        // Call the run method to register all the admin hooks.
        $plugin_admin->run();
        
        // Register unified AJAX actions pointing to object methods
        add_action('wp_ajax_aege_create_llms_file', [$plugin_admin, 'handle_create_llms_file_ajax']);
        add_action('wp_ajax_aege_detect_schemas', [$plugin_admin, 'handle_schema_detection_ajax']);
        add_action('wp_ajax_aege_copy_original_content', [$plugin_admin, 'handle_copy_original_content_ajax']);
        add_action('wp_ajax_aege_publish_llm', [$plugin_admin, 'handle_publish_llm_ajax']);
        add_action('wp_ajax_aege_bulk_update_metadata', [$plugin_admin, 'handle_batch_update_metadata']);
        add_action('wp_ajax_aege_get_post_count', [$plugin_admin, 'handle_get_post_count']);
    }
}

// Start the plugin.
run_aege();

// Load Yoast SEO integration if Yoast is active
if (class_exists('WPSEO_Meta')) {
    require AEGE_PLUGIN_DIR . 'includes/class-aege-yoast-integration.php';
}

// Load Rank Math integration if Rank Math is active
if (class_exists('RankMath')) {
    require AEGE_PLUGIN_DIR . 'includes/class-aege-rankmath-integration.php';
}

// Load SEOPress integration if SEOPress is active
if (function_exists('seopress_activation')) {
    require AEGE_PLUGIN_DIR . 'includes/class-aege-seopress-integration.php';
}

// Load All In One SEO integration if All In One SEO is active
if (defined('AIOSEO_VERSION')) {
    require AEGE_PLUGIN_DIR . 'includes/class-aege-aioseo-integration.php';
}