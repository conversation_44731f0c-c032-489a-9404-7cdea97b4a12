<?php
/**
 * AEGE Yoast SEO Integration
 *
 * Integrates AEGE with Yoast SEO's sitemap functionality
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/class-aege-sitemap-helper.php';
require_once __DIR__ . '/class-aege-seo-base.php';

class AEGE_Yoast_Integration extends AEGE_SEO_Base {
    
    /**
     * Initialize the integration
     */
    public function __construct() {
        parent::__construct();
        
        // Add our sitemap to Yoast's index - use proper Yoast hooks
        add_filter('wpseo_sitemap_index', array($this, 'add_to_index'), 10, 1);
        
        // Register our sitemap provider with Yoast (for newer versions)
        add_filter('wpseo_sitemaps_supported_providers', array($this, 'register_provider'), 10, 1);
        
        // Register custom sitemap (alternative method for compatibility)
        add_action('wpseo_register_extra_rewrites', array($this, 'register_custom_sitemap'));
    }
    
    /**
     * Add our sitemap to Yoast's index
     */
    public function add_to_index($content) {
        // Check if AEGE master switch is enabled
        $options = get_option('aege_settings');
        if (empty($options['master_switch'])) {
            return $content;
        }
        
        // Generate sitemap URL
        $sitemap_url = home_url('/aege-sitemap.xml');
        
        // Add our sitemap to the index
        $sitemap_entry = '
    <sitemap>
        <loc>' . esc_url($sitemap_url) . '</loc>
        <lastmod>' . AEGE_Sitemap_Helper::get_aege_sitemap_lastmod() . '</lastmod>
    </sitemap>';
        
        return $content . $sitemap_entry;
    }
    
    /**
     * Register our sitemap provider with Yoast
     */
    public function register_provider($providers) {
        // Add our provider
        $providers['aege'] = new AEGE_Yoast_Sitemap_Provider();
        return $providers;
    }
    
    /**
     * Register custom sitemap for compatibility
     */
    public function register_custom_sitemap() {
        // Add rewrite rule for our custom sitemap
        add_rewrite_rule('^aege-sitemap\\.xml$', 'index.php?aege_sitemap=1', 'top');
    }
    
    /**
     * Clear Yoast sitemap cache when AEGE cache is cleared
     */
    public function clear_sitemap_cache() {
        // Clear Yoast cache if active
        if (class_exists('WPSEO_Sitemaps_Cache')) {
            WPSEO_Sitemaps_Cache::clear();
        }
        
        // Also clear rewrite rules so they can be regenerated
        flush_rewrite_rules();
    }
    
    /**
     * Generate sitemap content
     *
     * @return string Sitemap XML content
     */
    public function generate_sitemap() {
        return AEGE_Standalone_Sitemap::generate_standalone_sitemap();
    }
    
    /**
     * Import schema data from Yoast SEO
     *
     * @param WP_Post $post The post object
     * @return array Schema data
     */
    protected function import_schema_data($post) {
        // Check if Yoast is set to noindex this post
        $robots = WPSEO_Meta::get_value('meta-robots-noindex', $post->ID);
        if ($robots === '1') {
            // Set a flag that this post should be noindex
            $this->aege_post_noindex = true;
        }
        
        // Try to get the actual schema data that Yoast generates
        // This is the proper way to get Yoast's schema data
        if (class_exists('WPSEO_Schema_Generator')) {
            try {
                // Get the schema context
                $schema_context = new WPSEO_Schema_Context();
                
                // Create schema generator
                $schema_generator = new WPSEO_Schema_Generator($schema_context);
                
                // Generate the schema data
                $schema_data = $schema_generator->generate();
                
                // If we got schema data, return it
                if (!empty($schema_data)) {
                    return $schema_data;
                }
            } catch (Exception $e) {
                // If there's an error, fall back to manual generation
            }
        }
        
        // Fallback: Try to get individual meta values from Yoast and build schema
        // Get title
        $title = WPSEO_Meta::get_value('title', $post->ID);
        if (empty($title)) {
            $title = get_the_title($post->ID);
        }
        
        // Get meta description
        $meta_description = WPSEO_Meta::get_value('metadesc', $post->ID);
        if (empty($meta_description)) {
            $meta_description = get_the_excerpt($post->ID);
        }
        
        // Build basic schema
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => get_permalink($post->ID),
            ),
            'headline' => $title,
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $post->post_author),
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_site_icon_url(),
                ),
            ),
            'description' => $meta_description,
        );
        
        return $schema;
    }
    
    /**
     * Get the plugin name
     *
     * @return string Plugin name
     */
    protected function get_plugin_name() {
        return 'Yoast SEO';
    }
}

/**
 * AEGE Yoast Sitemap Provider
 */
class AEGE_Yoast_Sitemap_Provider {
    
    /**
     * Get sitemap data for AEGE-enabled content
     */
    public function get_sitemap_data($type, $page) {
        // Get all AEGE-enabled posts
        $args = array(
            'post_type' => array('post', 'page'),
            'post_status' => 'publish',
            'posts_per_page' => 1000, // Large number to get all posts
            'meta_query' => array(
                array(
                    'key' => '_aege_enabled',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );
        
        $query = new WP_Query($args);
        $posts = $query->posts;
        
        $urls = array();
        
        foreach ($posts as $post) {
            $urls[] = array(
                'loc' => trailingslashit(get_permalink($post->ID)) . 'llm/',
                'lastmod' => get_the_modified_date('c', $post->ID),
                'changefreq' => 'weekly',
                'priority' => 0.8
            );
        }
        
        return $urls;
    }
}