<?php
/**
 * AEGE REST API Endpoints
 *
 * @package AEGE
 * @version 1.1.1
 */

class AEGE_REST_API {
    
    /**
     * Namespace for AEGE REST API endpoints
     */
    const API_NAMESPACE = 'aege/v1';
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Register content endpoints
        register_rest_route(self::API_NAMESPACE, '/content/(?P<id>\d+)', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_content'),
            'permission_callback' => array($this, 'authenticate_request_for_read'),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'validate_callback' => array($this, 'validate_post_id'),
                ),
            ),
        ));
        
        register_rest_route(self::API_NAMESPACE, '/content/(?P<id>\d+)', array(
            'methods' => WP_REST_Server::EDITABLE,
            'callback' => array($this, 'update_content'),
            'permission_callback' => array($this, 'authenticate_request_for_write'),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'validate_callback' => array($this, 'validate_post_id'),
                ),
                'content' => array(
                    'required' => false,
                    'validate_callback' => array($this, 'validate_content'),
                ),
                'title' => array(
                    'required' => false,
                    'validate_callback' => array($this, 'validate_title'),
                ),
                'meta_description' => array(
                    'required' => false,
                    'validate_callback' => array($this, 'validate_meta_description'),
                ),
            ),
        ));
        
        register_rest_route(self::API_NAMESPACE, '/content', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_content_list'),
            'permission_callback' => array($this, 'authenticate_request_for_read'),
            'args' => array(
                'post_type' => array(
                    'required' => false,
                    'default' => 'post',
                ),
                'per_page' => array(
                    'required' => false,
                    'default' => 10,
                    'sanitize_callback' => 'absint',
                ),
                'page' => array(
                    'required' => false,
                    'default' => 1,
                    'sanitize_callback' => 'absint',
                ),
            ),
        ));
        
        register_rest_route(self::API_NAMESPACE, '/content/(?P<id>\d+)/regenerate', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array($this, 'regenerate_content'),
            'permission_callback' => array($this, 'authenticate_request_for_write'),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'validate_callback' => array($this, 'validate_post_id'),
                ),
            ),
        ));
        
        // Register taxonomy endpoints
        register_rest_route(self::API_NAMESPACE, '/categories', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_categories'),
            'permission_callback' => array($this, 'authenticate_request_for_read'),
        ));
        
        register_rest_route(self::API_NAMESPACE, '/tags', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_tags'),
            'permission_callback' => array($this, 'authenticate_request_for_read'),
        ));
        
        // Register sitemap endpoint
        register_rest_route(self::API_NAMESPACE, '/sitemap', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_sitemap'),
            'permission_callback' => array($this, 'authenticate_request_for_read'),
        ));
    }

    /**
     * Authenticate REST API requests using Application Passwords
     */
    public function authenticate_request($request, $required_capability = 'edit_posts') {
        // Check rate limit
        if ($this->check_rate_limit($request)) {
            return $this->check_rate_limit($request);
        }

        // Check if Application Passwords are available (WordPress 5.6+)
        if (!class_exists('WP_Application_Passwords')) {
            return new WP_Error('aege_rest_error', 'Application Passwords not available. WordPress 5.6+ required.', array('status' => 500));
        }
        
        // WordPress automatically handles Application Password authentication
        // through the standard authentication process when the Authorization header is present
        $current_user_id = get_current_user_id();
        
        if (!$current_user_id) {
            return new WP_Error('aege_rest_error', 'Authentication required', array('status' => 401));
        }
        
        // Check if user has the required capability
        if (!user_can($current_user_id, $required_capability)) {
            return new WP_Error('aege_rest_error', 'Insufficient permissions', array('status' => 403));
        }
        
        return true;
    }

    /**
     * Authenticate REST API requests for write access using Application Passwords
     */
    public function authenticate_request_for_write($request) {
        return $this->authenticate_request($request, 'edit_posts');
    }

    /**
     * Authenticate REST API requests for read access using Application Passwords
     */
    public function authenticate_request_for_read($request) {
        return $this->authenticate_request($request, 'read');
    }

    /**
     * Check rate limit for REST API requests
     */
    public function check_rate_limit($request) {
        $user_id = get_current_user_id();
        if ($user_id) {
            $rate_limit_key = 'aege_rest_rate_limit_user_' . $user_id;
        } else {
            $rate_limit_key = 'aege_rest_rate_limit_ip_' . md5(sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'])));
        }

        $rate_limit = 100; // 100 requests per minute
        $time_frame = 60; // 60 seconds

        $request_count = get_transient($rate_limit_key);

        if ($request_count === false) {
            set_transient($rate_limit_key, 1, $time_frame);
        } else {
            if ($request_count > $rate_limit) {
                return new WP_Error('aege_rest_error', 'Rate limit exceeded. Please try again later.', array('status' => 429));
            }
            set_transient($rate_limit_key, $request_count + 1, $time_frame);
        }

        return false;
    }
    
    /**
     * Get AEGE-optimized content for a specific post
     */
    public function get_content($request) {
        $post_id = $request->get_param('id');
        $post = get_post($post_id);
        
        if (!$post) {
            return new WP_Error('aege_rest_error', 'Post not found', array('status' => 404));
        }
        
        // Check if AEGE is enabled for this post
        $aege_enabled = get_post_meta($post_id, '_aege_enabled', true);
        if (!$aege_enabled) {
            return new WP_Error('aege_rest_error', 'AEGE not enabled for this post', array('status' => 400));
        }
        
        // Get custom AEGE content if available
        $custom_content = get_post_meta($post_id, '_aege_custom_content', true);
        
        // Get schema data
        $generator = new AEGE_Generator();
        $schema_data = $generator->get_schema($post, $post->post_content);
        
        $response = array(
            'id' => $post_id,
            'title' => get_the_title($post_id),
            'content' => array(
                'auto_generated' => apply_filters('the_content', $post->post_content),
                'custom' => $custom_content,
                'active' => !empty($custom_content) ? $custom_content : apply_filters('the_content', $post->post_content),
            ),
            'schema' => $schema_data,
            'meta' => array(
                'excerpt' => get_the_excerpt($post_id),
                'date_published' => get_the_date('c', $post_id),
                'date_modified' => get_the_modified_date('c', $post_id),
                'author' => get_the_author_meta('display_name', $post->post_author),
            ),
            'aege_meta' => array(
                'enabled' => (bool) $aege_enabled,
                'llm_url' => trailingslashit(get_permalink($post_id)) . 'llm/',
                'last_updated' => get_post_meta($post_id, '_aege_last_updated', true),
            ),
        );
        
        return new WP_REST_Response($response, 200);
    }
    
    /**
     * Update custom AEGE content for a specific post
     */
    public function update_content($request) {
        $post_id = $request->get_param('id');
        $post = get_post($post_id);
        
        if (!$post) {
            return new WP_Error('aege_rest_error', 'Post not found', array('status' => 404));
        }
        
        // Check if AEGE is enabled for this post
        $aege_enabled = get_post_meta($post_id, '_aege_enabled', true);
        if (!$aege_enabled) {
            // Enable AEGE for this post if it's not already enabled
            update_post_meta($post_id, '_aege_enabled', '1');
        }
        
        $updated_fields = array();
        
        // Update custom content if provided
        if ($request->get_param('content') !== null) {
            $content = $request->get_param('content');
            update_post_meta($post_id, '_aege_custom_content', wp_kses_post($content));
            $updated_fields[] = 'content';
        }
        
        // Update title if provided
        if ($request->get_param('title') !== null) {
            $title = $request->get_param('title');
            wp_update_post(array(
                'ID' => $post_id,
                'post_title' => sanitize_text_field($title),
            ));
            $updated_fields[] = 'title';
        }
        
        // Update meta description if provided
        if ($request->get_param('meta_description') !== null) {
            $meta_desc = $request->get_param('meta_description');
            // Try to update meta description in popular SEO plugins
            $this->update_seo_meta_description($post_id, $meta_desc);
            $updated_fields[] = 'meta_description';
        }
        
        // Clear AEGE cache for this post
        if (class_exists('AEGE_Cache_Manager')) {
            AEGE_Cache_Manager::clear_cache_for_object($post_id, 'post');
        }
        
        // Update last modified timestamp
        update_post_meta($post_id, '_aege_last_updated', current_time('mysql'));
        
        $response = array(
            'success' => true,
            'message' => 'Content updated successfully',
            'updated_fields' => $updated_fields,
            'post_id' => $post_id,
        );
        
        return new WP_REST_Response($response, 200);
    }
    
    /**
     * Get list of AEGE-enabled content
     */
    public function get_content_list($request) {
        $post_type = $request->get_param('post_type');
        $per_page = $request->get_param('per_page');
        $page = $request->get_param('page');
        
        $args = array(
            'post_type' => $post_type,
            'posts_per_page' => $per_page,
            'paged' => $page,
            'meta_query' => array(
                array(
                    'key' => '_aege_enabled',
                    'value' => '1',
                    'compare' => '=',
                ),
            ),
        );
        
        $query = new WP_Query($args);
        $posts = $query->posts;
        
        $content_list = array();
        foreach ($posts as $post) {
            $content_list[] = array(
                'id' => $post->ID,
                'title' => get_the_title($post->ID),
                'post_type' => $post->post_type,
                'status' => $post->post_status,
                'date_published' => get_the_date('c', $post->ID),
                'date_modified' => get_the_modified_date('c', $post->ID),
                'has_custom_content' => (bool) get_post_meta($post->ID, '_aege_custom_content', true),
                'llm_url' => trailingslashit(get_permalink($post->ID)) . 'llm/',
            );
        }
        
        $response = array(
            'content' => $content_list,
            'total' => $query->found_posts,
            'pages' => $query->max_num_pages,
            'current_page' => $page,
        );
        
        return new WP_REST_Response($response, 200);
    }
    
    /**
     * Regenerate AEGE content for a specific post
     */
    public function regenerate_content($request) {
        $post_id = $request->get_param('id');
        $post = get_post($post_id);
        
        if (!$post) {
            return new WP_Error('aege_rest_error', 'Post not found', array('status' => 404));
        }
        
        // Clear custom content to force regeneration
        delete_post_meta($post_id, '_aege_custom_content');
        
        // Clear AEGE cache for this post
        if (class_exists('AEGE_Cache_Manager')) {
            AEGE_Cache_Manager::clear_cache_for_object($post_id, 'post');
        }
        
        // Update last modified timestamp
        update_post_meta($post_id, '_aege_last_updated', current_time('mysql'));
        
        $response = array(
            'success' => true,
            'message' => 'Content regeneration triggered successfully',
            'post_id' => $post_id,
            'llm_url' => trailingslashit(get_permalink($post_id)) . 'llm/',
        );
        
        return new WP_REST_Response($response, 200);
    }
    
    /**
     * Get categories with AEGE information
     */
    public function get_categories($request) {
        $categories = get_categories(array('hide_empty' => false));
        $category_list = array();
        
        foreach ($categories as $category) {
            $category_list[] = array(
                'id' => $category->term_id,
                'name' => $category->name,
                'description' => $category->description,
                'count' => $category->count,
                'llm_url' => trailingslashit(get_category_link($category->term_id)) . 'llm/',
            );
        }
        
        return new WP_REST_Response($category_list, 200);
    }
    
    /**
     * Get tags with AEGE information
     */
    public function get_tags($request) {
        $tags = get_tags(array('hide_empty' => false));
        $tag_list = array();
        
        foreach ($tags as $tag) {
            $tag_list[] = array(
                'id' => $tag->term_id,
                'name' => $tag->name,
                'description' => $tag->description,
                'count' => $tag->count,
                'llm_url' => trailingslashit(get_tag_link($tag->term_id)) . 'llm/',
            );
        }
        
        return new WP_REST_Response($tag_list, 200);
    }
    
    /**
     * Get AEGE sitemap data
     */
    public function get_sitemap($request) {
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-standalone-sitemap.php';
        $sitemap_data = AEGE_Standalone_Sitemap::get_sitemap_data();
        return new WP_REST_Response($sitemap_data, 200);
    }
    
    /**
     * Update SEO meta description in popular SEO plugins
     */
    private function update_seo_meta_description($post_id, $meta_description) {
        // Update Yoast SEO meta description
        if (class_exists('WPSEO_Meta')) {
            update_post_meta($post_id, '_yoast_wpseo_metadesc', sanitize_text_field($meta_description));
        }
        
        // Update Rank Math meta description
        if (class_exists('RankMath')) {
            update_post_meta($post_id, 'rank_math_description', sanitize_text_field($meta_description));
        }
        
        // Update SEOPress meta description
        if (function_exists('seopress_activation')) {
            update_post_meta($post_id, '_seopress_titles_desc', sanitize_text_field($meta_description));
        }
        
        // Update All In One SEO meta description
        if (defined('AIOSEO_VERSION')) {
            update_post_meta($post_id, '_aioseo_description', sanitize_text_field($meta_description));
        }
    }
    
    /**
     * Validate post ID
     */
    public function validate_post_id($param, $request, $key) {
        return is_numeric($param) && get_post($param);
    }
    
    /**
     * Validate content
     */
    public function validate_content($param, $request, $key) {
        return is_string($param);
    }
    
    /**
     * Validate title
     */
    public function validate_title($param, $request, $key) {
        return is_string($param);
    }
    
    /**
     * Validate meta description
     */
    public function validate_meta_description($param, $request, $key) {
        return is_string($param);
    }
}
