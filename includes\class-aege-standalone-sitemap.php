<?php
/**
 * AEGE Standalone Sitemap Generator
 *
 * Provides a fallback sitemap generation method that doesn't rely on Yoast integration
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/class-aege-sitemap-helper.php';

class AEGE_Standalone_Sitemap {
    
    /**
     * Handle sitemap requests directly
     */
    public static function handle_sitemap_request() {
        // Check if this is a request for our sitemap
        if (isset($_SERVER['REQUEST_URI']) && 
            (strpos(sanitize_text_field(wp_unslash($_SERVER['REQUEST_URI'])), 'aege-sitemap.xml') !== false || 
             strpos(sanitize_text_field(wp_unslash($_SERVER['REQUEST_URI'])), 'aege_sitemap.xml') !== false)) {
            
            // Generate and output the sitemap
            status_header(200);
            header('Content-Type: application/xml; charset=utf-8');
            echo self::generate_standalone_sitemap();
            exit;
        }
    }
    
    /**
     * Generate the standalone sitemap
     */
    public static function generate_standalone_sitemap() {
        $sitemap_data = self::get_sitemap_data();

        if (empty($sitemap_data)) {
            return self::get_empty_sitemap();
        }

        $sitemap_content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $sitemap_content .= "<!-- XML Sitemap for LLM-optimized content used by AEGE Optimizer Plugin -->\n";
        $sitemap_content .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

        foreach ($sitemap_data as $url_data) {
            $sitemap_content .= "\t<url>\n";
            $sitemap_content .= "\t\t<loc>" . esc_url($url_data['url']) . "</loc>\n";
            $sitemap_content .= "\t\t<lastmod>" . esc_xml($url_data['lastmod']) . "</lastmod>\n";
            $sitemap_content .= "\t\t<changefreq>" . esc_xml($url_data['changefreq']) . "</changefreq>\n";
            $sitemap_content .= "\t\t<priority>" . esc_xml($url_data['priority']) . "</priority>\n";
            $sitemap_content .= "\t</url>\n";
        }

        $sitemap_content .= "</urlset>\n";

        return $sitemap_content;
    }

    public static function get_sitemap_data() {
        // Check if AEGE is enabled
        $options = get_option('aege_settings');
        if (empty($options['master_switch'])) {
            return array();
        }

        // Get all AEGE-enabled content
        $aege_posts = AEGE_Sitemap_Helper::get_aege_enabled_posts();
        $aege_pages = AEGE_Sitemap_Helper::get_aege_enabled_pages();
        $aege_categories = AEGE_Sitemap_Helper::get_aege_enabled_categories();
        $aege_tags = AEGE_Sitemap_Helper::get_aege_enabled_tags();

        $has_content = !empty($aege_posts) || !empty($aege_pages) || !empty($aege_categories) || !empty($aege_tags);

        if (!$has_content) {
            return array();
        }

        $sitemap_data = array();
        $added_urls = array();

        // Add each AEGE-enabled post to the sitemap
        if (!empty($aege_posts)) {
            foreach ($aege_posts as $post) {
                $aege_url = trailingslashit(get_permalink($post->ID)) . 'llm/';
                if (in_array($aege_url, $added_urls)) {
                    continue;
                }
                $added_urls[] = $aege_url;

                $sitemap_data[] = array(
                    'url' => $aege_url,
                    'lastmod' => get_post_modified_time('c', true, $post->ID),
                    'changefreq' => 'weekly',
                    'priority' => '0.8',
                );
            }
        }

        // Add each AEGE-enabled page to the sitemap
        if (!empty($aege_pages)) {
            foreach ($aege_pages as $page) {
                $aege_url = trailingslashit(get_permalink($page->ID)) . 'llm/';
                if (in_array($aege_url, $added_urls)) {
                    continue;
                }
                $added_urls[] = $aege_url;

                $sitemap_data[] = array(
                    'url' => $aege_url,
                    'lastmod' => get_post_modified_time('c', true, $page->ID),
                    'changefreq' => 'weekly',
                    'priority' => '0.8',
                );
            }
        }

        // Add each AEGE-enabled category to the sitemap
        if (!empty($aege_categories)) {
            foreach ($aege_categories as $category) {
                $aege_url = trailingslashit(get_category_link($category->term_id)) . 'llm/';
                if (in_array($aege_url, $added_urls)) {
                    continue;
                }
                $added_urls[] = $aege_url;

                $sitemap_data[] = array(
                    'url' => $aege_url,
                    'lastmod' => date('c'),
                    'changefreq' => 'weekly',
                    'priority' => '0.6',
                );
            }
        }

        // Add each AEGE-enabled tag to the sitemap
        if (!empty($aege_tags)) {
            foreach ($aege_tags as $tag) {
                $aege_url = trailingslashit(get_tag_link($tag->term_id)) . 'llm/';
                if (in_array($aege_url, $added_urls)) {
                    continue;
                }
                $added_urls[] = $aege_url;

                $sitemap_data[] = array(
                    'url' => $aege_url,
                    'lastmod' => date('c'),
                    'changefreq' => 'weekly',
                    'priority' => '0.6',
                );
            }
        }

        return $sitemap_data;
    }
    
    /**
     * Get empty sitemap
     */
    private static function get_empty_sitemap() {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"></urlset>\n";
    }
}