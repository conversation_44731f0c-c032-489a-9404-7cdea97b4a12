
<?php
/**
 * AEGE Rank Math SEO Integration
 *
 * Integrates AEGE with Rank Math's sitemap functionality
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/class-aege-sitemap-helper.php';

/**
 * Register the AEGE sitemap provider with Rank Math
 */
add_filter('rank_math/sitemap/providers', function($providers) {
    $providers['aege'] = new AEGE_RankMath_Sitemap_Provider();
    return $providers;
});

/**
 * Clear Rank Math sitemap cache when AEGE cache is cleared
 */
add_action('aege_cache_cleared', function() {
    // Clear RankMath cache if active
    if (class_exists('\RankMath\Sitemap\Cache')) {
        \RankMath\Sitemap\Cache::invalidate_storage();
    }
});

add_action('aege_object_cache_cleared', function($post_id) {
    // Clear RankMath cache if active
    if (class_exists('\RankMath\Sitemap\Cache')) {
        \RankMath\Sitemap\Cache::invalidate_storage();
    }
});

/**
 * AEGE Rank Math Sitemap Provider
 */
class AEGE_RankMath_Sitemap_Provider {
    
    /**
     * Get sitemap data for AEGE-enabled content
     */
    public function get_sitemap_data($type, $page) {
        if ($type !== 'aege') {
            return false;
        }
        
        // Get all AEGE-enabled content
        $posts = AEGE_Sitemap_Helper::get_aege_enabled_posts();
        $pages = AEGE_Sitemap_Helper::get_aege_enabled_pages();
        $categories = AEGE_Sitemap_Helper::get_aege_enabled_categories();
        $tags = AEGE_Sitemap_Helper::get_aege_enabled_tags();
        
        $has_content = !empty($posts) || !empty($pages) || !empty($categories) || !empty($tags);
        
        if (!$has_content) {
            return false;
        }
        
        $data = array();
        
        // Add AEGE-enabled posts
        if (!empty($posts)) {
            foreach ($posts as $post) {
                $aege_url = trailingslashit(get_permalink($post->ID)) . 'llm/';
                
                $data[] = array(
                    'loc' => $aege_url,
                    'lastmod' => get_post_modified_time('c', true, $post->ID),
                    'changefreq' => 'weekly',
                    'priority' => '0.8'
                );
            }
        }
        
        // Add AEGE-enabled pages
        if (!empty($pages)) {
            foreach ($pages as $page) {
                $aege_url = trailingslashit(get_permalink($page->ID)) . 'llm/';
                
                $data[] = array(
                    'loc' => $aege_url,
                    'lastmod' => get_post_modified_time('c', true, $page->ID),
                    'changefreq' => 'weekly',
                    'priority' => '0.8'
                );
            }
        }
        
        // Add AEGE-enabled categories
        if (!empty($categories)) {
            foreach ($categories as $category) {
                $aege_url = trailingslashit(get_category_link($category->term_id)) . 'llm/';
                
                $data[] = array(
                    'loc' => $aege_url,
                    'lastmod' => date('c'), // Categories don't have a direct modification time
                    'changefreq' => 'weekly',
                    'priority' => '0.6'
                );
            }
        }
        
        // Add AEGE-enabled tags
        if (!empty($tags)) {
            foreach ($tags as $tag) {
                $aege_url = trailingslashit(get_tag_link($tag->term_id)) . 'llm/';
                
                $data[] = array(
                    'loc' => $aege_url,
                    'lastmod' => AEGE_Sitemap_Helper::get_last_modified_date_for_term($tag->term_id, 'post_tag'),
                    'changefreq' => 'weekly',
                    'priority' => '0.6'
                );
            }
        }
        
        return $data;
    }
    
    /**
     * Get post types to register with sitemap
     */
    public function get_post_types() {
        return array('aege');
    }
    
    /**
     * Get taxonomies to register with sitemap
     */
    public function get_taxonomies() {
        return array();
    }
}
