<?php
/**
 * Class AEGE_Main
 *
 * @package AEGE
 */

class AEGE_Main {

    protected $loader;
    protected $plugin_name;
    protected $version;

    public function __construct() {
        $this->plugin_name = 'aege';
        $this->version = AEGE_VERSION;

        $this->load_dependencies();
        $this->define_public_hooks();
        // REMOVE the call to define_admin_hooks() from here.
    }

    private function load_dependencies() {
        // The class responsible for orchestrating the admin area
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-admin.php';

        // The class responsible for generating the /llm/ page content
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-generator.php';
        
        // The class responsible for managing the cache
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-cache-manager.php';
        
        // The class responsible for cleaning content
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-content-cleaner.php';
        
        // The class responsible for generating summaries
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-summary-generator.php';
        
        // The class responsible for entity analysis
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-entity-analyzer.php';
        
        // The class responsible for REST API endpoints
        require_once AEGE_PLUGIN_DIR . 'includes/class-aege-rest-api.php';
    }

    // This method is the problem. It's trying to call a private constructor.
    // We will REMOVE this method from AEGE_Main.
    /*
    private function define_admin_hooks() {
        $plugin_admin = AEGE_Admin::get_instance( $this->get_plugin_name(), $this->get_version() );

        // Add the main settings page
        add_action( 'admin_menu', array( $plugin_admin, 'add_admin_menu' ) );
        
        // Register the settings themselves
        add_action( 'admin_init', array( $plugin_admin, 'register_settings' ) );

        // Add the metabox to post edit screens
        add_action( 'add_meta_boxes', array( $plugin_admin, 'add_meta_box' ) );
        
        // Handle saving the metabox data
        add_action( 'save_post', array( $plugin_admin, 'save_meta_box_data' ) );
        
        // Handle AJAX requests for llms.txt creation
        add_action( 'wp_ajax_aege_create_llms_file', array( $plugin_admin, 'handle_create_llms_file' ) );
        
        // Handle AJAX requests for schema detection
        add_action( 'wp_ajax_aege_detect_schemas', array( $plugin_admin, 'handle_schema_detection' ) );
        
        // Handle AJAX requests for copying original content
        add_action( 'wp_ajax_aege_copy_original_content', array( $plugin_admin, 'handle_copy_original_content' ) );
    }
    */

    private function define_public_hooks() {
        // Register the /llm/ endpoint
        add_action( 'init', array( $this, 'register_aege_rewrite_rules_and_tags' ) );
        
        // Intercept requests for the /llm/ endpoint
        add_action( 'template_redirect', array( $this, 'template_redirect_intercept' ) );
        
        // Prevent canonical redirect for AEGE pages
        add_filter( 'redirect_canonical', array( $this, 'prevent_canonical_redirect_for_llm' ), 10, 2 );

        // Add the alternate link to the head of pages
        add_action( 'wp_head', array( $this, 'add_alternate_link' ) );
        
        // Register REST API endpoints
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Registers all rewrite rules, tags, and query vars for AEGE.
     * This is a more robust method than just calling add_rewrite_rule directly.
     */
    public function register_aege_rewrite_rules_and_tags() {
        // Add our custom query variables
        add_filter('query_vars', function($vars) {
            $vars[] = 'is_aege_page';
            $vars[] = 'aege_slug';
            $vars[] = 'aege_sitemap';
            return $vars;
        });

        // A SINGLE, UNAMBIGUOUS RULE FOR ALL POSTS AND PAGES
        add_rewrite_rule(
            '(.+?)/llm/?$',
            'index.php?aege_slug=$matches[1]&is_aege_page=1', // Use our custom query var
            'top'
        );

        // Your existing, correct rules for taxonomies
        add_rewrite_rule('category/(.+?)/llm/?$', 'index.php?category_name=$matches[1]&is_aege_page=1', 'top');
        add_rewrite_rule('tag/(.+?)/llm/?$', 'index.php?tag=$matches[1]&is_aege_page=1', 'top');
        
        // Add rewrite rule for AEGE sitemap
        add_rewrite_rule('^aege-sitemap\.xml$', 'index.php?aege_sitemap=1', 'top');
    }

    /**
     * Prevents WordPress from performing a canonical redirect on our /llm/ pages.
     * This is the key to stopping the unwanted redirect for pages.
     *
     * @param string $redirect_url  The redirect URL.
     * @param string $requested_url The requested URL.
     * @return string|false The redirect URL or false to prevent the redirect.
     */
    public function prevent_canonical_redirect_for_llm($redirect_url, $requested_url) {
        // If our custom query variable is set, it means one of our rewrite rules matched.
        // In this case, we want to handle the request ourselves and prevent any redirects.
        if (get_query_var('is_aege_page')) {
            return false; // Returning false cancels the redirect.
        }

        // For all other URLs, let WordPress behave as normal.
        return $redirect_url;
    }

    /**
     * Intercepts requests for /llm/ pages, finds the correct content, and loads the template.
     * This is the definitive version that handles posts, pages, and taxonomies correctly.
     */
    public function template_redirect_intercept() {
        // Only proceed if our main query var is set by one of our rewrite rules.
        if (!get_query_var('is_aege_page')) {
            return;
        }

        $options = get_option('aege_settings');
        $workflow = isset($options['workflow']) ? $options['workflow'] : 'automated_override';

        global $wp_query;
        $object_id = 0;
        $queried_object = null;

        // --- Step 1: Check for specific taxonomy matches first ---
        if (get_query_var('category_name')) {
            $queried_object = get_term_by('slug', get_query_var('category_name'), 'category');
        } elseif (get_query_var('tag')) {
            $queried_object = get_term_by('slug', get_query_var('tag'), 'post_tag');
        }

        if ($queried_object && !is_wp_error($queried_object)) {
            // We found a category or tag. Render the AEGE page for categories/tags and exit.
            $generator = new AEGE_Generator();
            $generator->render_aege_page();
            exit();
        }

        // --- Step 2: If not a taxonomy, resolve the general post/page slug ---
        $aege_slug = get_query_var('aege_slug');
        if ($aege_slug) {
            // get_page_by_path() is the most reliable way to find a post or page by its slug/path.
            $found_post = get_page_by_path($aege_slug, OBJECT, ['post', 'page']);

            // For published posts, proceed as normal
            // For draft/unpublished posts, we still want to show the AEGE page if the is_aege_page parameter is set
            if ($found_post && ($found_post->post_status === 'publish' || get_query_var('p'))) {
                $object_id = $found_post->ID;
                $queried_object = $found_post;
            }
        } else {
            // Handle the fallback case where we're using ?p=POST_ID&is_aege_page=1 (for draft posts)
            $post_id = get_query_var('p');
            if ($post_id) {
                $found_post = get_post($post_id);
                if ($found_post) {
                    $object_id = $found_post->ID;
                    $queried_object = $found_post;
                }
            }
        }

        // --- Step 3: Final Validation ---
        if (empty($object_id)) {
            $wp_query->set_404();
            status_header(404);
            return;
        }

        if ($workflow === 'manual_opt_in') {
            if (get_post_meta($object_id, '_aege_enabled', true) !== '1') {
                $wp_query->set_404();
                status_header(404);
                return;
            }
        }

        // --- Step 4: Success! We have a valid object. Set up the WordPress environment. ---
        // This is crucial for template tags like the_title(), the_content(), etc., to work.
        $wp_query->is_404 = false; // It's not a 404
        $wp_query->is_page = ($queried_object->post_type == 'page');
        $wp_query->is_single = ($queried_object->post_type == 'post');
        $wp_query->is_singular = true;
        $wp_query->queried_object = $queried_object;
        $wp_query->queried_object_id = $object_id;
        
        // Set the global $post variable, which many functions rely on.
        $GLOBALS['post'] = $queried_object;
        setup_postdata($queried_object);

        // --- Step 5: Load Your Custom Template ---
        // Now that the environment is correctly set up, render the AEGE page and exit.
        $generator = new AEGE_Generator();
        $generator->render_aege_page();
        exit();
    }

    public function add_alternate_link() {
        $options = get_option('aege_settings');
        if ( empty( $options['master_switch'] ) ) {
            return;
        }

        // Handle single posts/pages
        if ( is_singular( array_keys($options['post_types']) ) ) {
            $post_id = get_the_ID();
            if ( get_post_meta( $post_id, '_aege_enabled', true ) ) {
                $aege_url = trailingslashit( get_permalink( $post_id ) ) . 'llm/';
                echo '<link rel="alternate" href="' . esc_url( $aege_url ) . '" type="text/html+aege" title="AEGE Optimized Version" />' . "\n";
            }
        }
        // Handle category archives
        else if ( is_category() && isset($options['post_types']['post']) && $options['post_types']['post'] == 1 ) {
            $category_id = get_queried_object_id();
            $aege_url = trailingslashit( get_category_link( $category_id ) ) . 'llm/';
            echo '<link rel="alternate" href="' . esc_url( $aege_url ) . '" type="text/html+aege" title="AEGE Optimized Version" />' . "\n";
        }
        // Handle tag archives
        else if ( is_tag() && isset($options['post_types']['post']) && $options['post_types']['post'] == 1 ) {
            $tag_id = get_queried_object_id();
            $aege_url = trailingslashit( get_tag_link( $tag_id ) ) . 'llm/';
            echo '<link rel="alternate" href="' . esc_url( $aege_url ) . '" type="text/html+aege" title="AEGE Optimized Version" />' . "\n";
        }
    }

    public function run() {
        // This function doesn't need to do anything itself since we are using add_action
        // It's here to maintain a logical structure
    }

    public function get_plugin_name() {
        return $this->plugin_name;
    }

    public function get_version() {
        return $this->version;
    }
    
    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        // Check if REST API is enabled in settings
        $options = get_option('aege_settings');
        if (isset($options['enable_rest_api']) && $options['enable_rest_api'] == 1) {
            $rest_api = new AEGE_REST_API();
            $rest_api->register_routes();
        }
    }
}