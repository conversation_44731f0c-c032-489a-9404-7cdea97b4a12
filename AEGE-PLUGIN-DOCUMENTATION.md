# AEGE WordPress Plugin - Comprehensive Documentation

## Overview
AEGE (Answer Engine and Generative Engine) is a sophisticated WordPress plugin designed to optimize content for AI-driven platforms including search engines and large language models (LLMs). It creates AI-friendly versions of your content accessible through `/llm/` endpoints and provides tools for AI content discovery and management.

## Testing
AEGE includes a comprehensive test suite to ensure code quality and prevent regressions. Tests are located in the `tests/` directory and include both unit and integration tests.

To run tests:
```bash
vendor/bin/phpunit
```

See `tests/README.md` for detailed testing instructions.

New tests have been added for the Citation & Source Enhancement feature:
- `tests/unit/test-citation-enhancement.php` - Tests for outbound link security enhancement and citation marking
- `tests/unit/test-citation-analyzer.php` - Tests for citation analysis utilities
- `tests/unit/test-seo-base.php` - Tests for SEO base class functionality

The dashboard functionality is also covered by existing tests, with additional tests planned for the new AEO/GEO Scorecard features.

## Core Architecture

### Plugin Structure
```
aege/
├── aege.php                    # Main plugin file
├── includes/
│   ├── class-aege-main.php     # Main plugin orchestrator
│   ├── class-aege-admin.php    # Admin interface management
│   ├── class-aege-generator.php # Content generation and optimization
│   ├── class-aege-content-cleaner.php # Content cleaning and processing
│   ├── class-aege-cache-manager.php # Caching system
│   ├── class-aege-database-cache.php # Database caching implementation
│   ├── class-aege-summary-generator.php # Content summarization
│   ├── class-aege-rest-api.php # REST API endpoints
│   ├── class-aege-standalone-sitemap.php # Sitemap generation
│   ├── class-aege-activator.php # Plugin activation
│   ├── class-aege-deactivator.php # Plugin deactivation
│   ├── class-aege-seo-base.php # Abstract base class for SEO integrations
│   ├── class-aege-citation-analyzer.php # Citation analysis utilities
│   └── [SEO Integration Classes] # Yoast, RankMath, etc.
├── admin/
│   └── js/
│       └── aege-dashboard.js   # Admin dashboard JavaScript
├── templates/
│   ├── aege-template.php       # Main content template
│   └── aege-term-template.php  # Taxonomy template
└── README.txt                  # Plugin description

### Main Plugin File (`aege.php`)
The main plugin file handles:
- Plugin activation/deactivation hooks
- Loading of core classes
- Initialization of cache management
- Handling of sitemap and llms.txt requests
- Registration of WordPress hooks and filters
- Integration with popular SEO plugins

### Content Cleaner (`class-aege-content-cleaner.php`)
Processes content to make it more AI-friendly while preserving important structure:
- HTML sanitization with `wp_kses`
- Shortcode removal
- Emoji removal
- Whitespace normalization
- Empty tag removal
- Outbound link enhancement with security attributes
- Citation marking with semantic attributes
- Schema detection and generation

### Admin Interface (`class-aege-admin.php`)
Manages the WordPress admin interface:
- Settings pages
- Dashboard functionality
- Metaboxes for post editing
- AJAX handlers
- System status checks
- Content analysis tools
- AEO Score column for Posts and Pages lists

### Database Cache (`class-aege-database-cache.php`)
Implements persistent caching in a custom database table:
- Cache storage and retrieval
- Cache invalidation
- Performance optimization

### REST API (`class-aege-rest-api.php`)
Provides programmatic access to AEGE functionality:
- Custom endpoints for content processing
- Integration with WordPress REST API

## Development Conventions

### Coding Standards
The project follows WordPress coding standards, enforced by PHP_CodeSniffer:
- PSR-4 class naming conventions
- WordPress PHP coding standards
- Inline documentation for all functions and classes

### Git Workflow
- Feature branches for new functionality
- Pull requests for code review
- Semantic versioning for releases
- Comprehensive commit messages

### Testing Practices
- Unit tests for individual functions
- Integration tests for complex workflows
- Test coverage for new features
- Continuous integration with automated testing

## Building and Running

### Development Commands
```bash
# Install dependencies
composer install

# Run tests
composer test

# Lint code
composer lint

# Fix code style issues
composer fix

# Run specific test suites
composer test-unit
composer test-master-switch
composer test-admin-scripts
```

### Plugin Activation
1. Upload plugin files to `/wp-content/plugins/aege/`
2. Activate through the WordPress Plugins screen
3. Configure settings through the AEGE Optimizer menu

## Recent Enhancements

### AEO Score Column
Enhanced the WordPress admin interface with a sortable AEO Score column in Posts and Pages lists:
- Color-coded scores (Green: 75-100, Red: 0-49, Neutral: 50-74)
- Clickable scores that link to detailed content analysis
- Professional "llm" text header with clean styling
- Proper positioning after the Date column
- Works reliably within WordPress table constraints

The AEO Score column now features a clean, professional text header labeled "llm" instead of an icon. This text header is styled to match the native WordPress admin interface with semibold text and proper alignment with sorting arrows. This approach eliminates previous issues with icon clipping and tooltip positioning while maintaining a clean, professional appearance. The column width has been increased to 75px to prevent text cutoff.

### Citation & Source Enhancement
The plugin now includes intelligent citation detection with two modes:

1. **Automatic Mode (Default)**: Treats all external links as citations unless explicitly excluded
   - Excludes links with `class="no-citation"`
   - Excludes links with `rel="sponsored"` or `rel="ugc"`
   - Adds `rel="noopener noreferrer"` for security

2. **Manual Mode**: Preserves existing behavior requiring `class="citation"` for citation counting

### AEO/GEO Scorecard Dashboard
Enhanced content analysis dashboard with:
- Schema Coverage metrics
- Citation Density metrics
- Content Freshness metrics
- Visualizations for all metrics

### Enhanced Schema Detection
Improved accuracy in detecting structured content with:

1. **Advanced HowTo Schema Detection**:
   - Title analysis for "How to" keywords
   - Command verb detection in headings (Ask, Search, Give, etc.)
   - Combined signal approach for more reliable detection
   - Flexible step extraction for varied content structures

2. **Enhanced FAQ Schema Detection**:
   - Multiple detection methods (headings, Q&A patterns, FAQ sections)
   - Improved duplicate handling with similarity matching
   - Better pattern recognition for varied Q&A formats

### Security Improvements
1. **Sanitized Server Variable Access**:
   - Proper sanitization of `$_SERVER['REMOTE_ADDR']` throughout the codebase
   - Consistent use of `sanitize_text_field(wp_unslash(...))` for all server variables
   - Enhanced security for all AJAX handlers and logging functions

### Performance Optimizations
1. **Improved Bulk Update Process**:
   - Enhanced error handling and logging
   - Better resource management during batch processing
   - More reliable metadata calculation for large content sets

## Troubleshooting

### Common Issues
1. **Endpoints not working**: Flush rewrite rules after plugin activation
2. **llms.txt not generating**: Check file permissions in WordPress root
3. **Caching issues**: Clear AEGE cache when content needs to be refreshed
4. **SEO plugin conflicts**: Ensure proper integration settings

### System Status Checks
The plugin includes a comprehensive health check system that verifies:
- Plugin master switch status
- llms.txt file presence
- robots.txt configuration
- Database cache table existence
- WordPress REST API status
- Key takeaways setting
- Workflow configuration

## Session Summary: Fixing /llm/ Endpoint Issues and Enhancing Schema Detection

### Problem Identified
The `/llm/` endpoints for pages were redirecting to normal page content instead of showing the AEGE-optimized version. Additionally, schema detection was missing many legitimate HowTo and FAQ content pieces due to overly rigid pattern matching.

### Root Cause
1. **Rule Priority Conflict**: WordPress's `redirect_canonical()` function was redirecting `/llm/` URLs to canonical page URLs before AEGE's template redirect handler could process them
2. **Ambiguous Rewrite Rules**: Separate rewrite rules for posts and pages were causing conflicts in rule matching priority
3. **Rigid Schema Detection**: Original pattern matching was too strict, missing HowTo content that used command verbs instead of explicit "Step" formatting
4. **Security Vulnerabilities**: Insecure access to `$_SERVER['REMOTE_ADDR']` without proper sanitization in multiple locations

### Solution Implemented
1. **Simplified Rewrite Rules**: Implemented a single, unambiguous rewrite rule for all posts and pages:
   ```php
   add_rewrite_rule('(.+?)/llm/?, 'index.php?aege_slug=$matches[1]&is_aege_page=1', 'top');
   ```

2. **Added Custom Query Variables**: Introduced `aege_slug` and `is_aege_page` query variables for better URL handling

3. **Enhanced Template Redirect Handler**: Completely rewrote the `template_redirect_intercept` function to:
   - First check for taxonomy matches (categories/tags)
   - Use `get_page_by_path()` to resolve post/page slugs reliably
   - Properly set up WordPress environment variables
   - Include strict validation for AEGE-enabled content

4. **Prevented Canonical Redirects**: Added a filter to prevent WordPress from redirecting AEGE URLs:
   ```php
   add_filter('redirect_canonical', [$this, 'prevent_canonical_redirect_for_llm'], 10, 2);
   ```

5. **Enhanced Schema Detection**:
   - **HowTo Schema**: Added multi-signal detection combining title analysis, command verb detection, and flexible pattern matching
   - **FAQ Schema**: Implemented multiple detection methods with improved duplicate handling
   - **Author Information**: Leveraged existing author schema inclusion in main generator

6. **Security Improvements**:
   - Replaced all instances of `'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'` with proper sanitization:
     ```php
     'ip' => sanitize_text_field(wp_unslash($_SERVER['REMOTE_ADDR'] ?? 'unknown'))
     ```
   - Applied consistent security practices across all AJAX handlers and logging functions

### Key Improvements
- **Unified Approach**: Single rewrite rule handles both posts and pages
- **Reliable Resolution**: Uses `get_page_by_path()` for accurate content identification
- **Canonical Redirect Prevention**: Stops WordPress from interfering with AEGE URLs
- **Better Error Handling**: Clear validation and 404 handling
- **Proper Environment Setup**: Ensures all WordPress template functions work correctly
- **Enhanced Schema Detection**: More flexible pattern matching for HowTo and FAQ schemas
- **Improved Security**: Proper sanitization of all server variables
- **Better Performance**: Optimized bulk update process with enhanced error handling

### Testing
After implementing these changes:
1. Flush rewrite rules via Settings > Permalinks
2. Test `/llm/` endpoints for both posts and pages
3. Verify taxonomy endpoints (categories/tags) still work correctly
4. Confirm proper 404 handling for non-existent content
5. Test schema detection with various HowTo and FAQ content formats
6. Verify bulk update functionality works without errors

These changes resolved the redirect issues while significantly improving schema detection accuracy and overall plugin security.