<?php
/**
 * The template for displaying AEGE-optimized term content.
 *
 * This template is designed for maximum machine-readability.
 * It is intentionally simple and devoid of styling.
 *
 * @package AEGE
 * @version 1.1.1
 */

// We should have these variables passed from the generator class.
// Add checks to ensure they exist to prevent errors.
if ( ! isset( $aege_term, $aege_posts, $aege_description, $aege_schema ) ) {
    // If the variables aren't set, something went wrong.
    // We can output an HTML comment for debugging.
    echo '<!-- AEGE Term Template Error: Required variables not found. -->';
    return;
}

$taxonomy_labels = get_taxonomy_labels( get_taxonomy( $aege_term->taxonomy ) );

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>AEGE Version of <?php echo esc_html( $taxonomy_labels->singular_name ); ?>: <?php echo esc_html( $aege_term->name ); ?></title>
    
    <?php
    /**
     * AEGE pages should generally not be indexed to avoid duplicate content issues.
     * The canonical link points to the original page which should be indexed instead.
     */
    ?>
    <meta name="robots" content="noindex, follow" />
    
    <?php
    /**
     * CRUCIAL: The canonical link MUST point back to the original user-facing page.
     * This tells search engines that this is an alternate version, not duplicate content.
     */
    ?>
    <link rel="canonical" href="<?php echo esc_url( get_term_link( $aege_term ) ); ?>" />

    <?php
    /**
     * The generated Schema.org JSON-LD is injected directly into the head.
     * This is the most important part for machine understanding.
     */
    ?>
    <script type="application/ld+json">
        <?php echo wp_json_encode( $aege_schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES ); ?>
    </script>
</head>
<body>

    <main>
        <article>
            <header>
                <h1><?php echo esc_html( $taxonomy_labels->singular_name ); ?>: <?php echo esc_html( $aege_term->name ); ?></h1>
            </header>
            
            <?php if ( ! empty( $aege_description ) ) : ?>
            <div class="aege-description">
                <?php echo wp_kses_post( $aege_description ); ?>
            </div>
            <?php endif; ?>
            
            <?php if ( ! empty( $aege_posts ) ) : ?>
            <div class="aege-posts-list">
                <h2>Posts in this <?php echo esc_html( strtolower( $taxonomy_labels->singular_name ) ); ?></h2>
                <ul>
                    <?php foreach ( $aege_posts as $post ) : ?>
                    <li>
                        <a href="<?php echo esc_url( trailingslashit( get_permalink( $post->ID ) ) . 'llm/' ); ?>">
                            <?php echo esc_html( get_the_title( $post->ID ) ); ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
        </article>
    </main>

</body>
</html>
