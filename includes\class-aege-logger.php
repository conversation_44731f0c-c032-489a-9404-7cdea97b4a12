<?php
/**
 * AEGE Logger Class
 *
 * Provides comprehensive logging functionality for the AEGE plugin
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

class AEGE_Logger {
    /**
     * Log levels
     */
    const EMERGENCY = 'emergency';
    const ALERT     = 'alert';
    const CRITICAL  = 'critical';
    const ERROR     = 'error';
    const WARNING   = 'warning';
    const NOTICE    = 'notice';
    const INFO      = 'info';
    const DEBUG     = 'debug';

    /**
     * Log a message
     *
     * @param string $level   Log level
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function log($level, $message, $context = array()) {
        // Get plugin settings to check if logging is enabled
        $options = get_option('aege_settings', array());
        $logging_enabled = isset($options['enable_logging']) ? (bool) $options['enable_logging'] : false;
        
        // If logging is not enabled, return early
        if (!$logging_enabled) {
            return;
        }
        
        // Get log level setting
        $log_level = isset($options['log_level']) ? $options['log_level'] : self::ERROR;
        
        // Check if we should log this message based on log level
        if (!self::should_log($level, $log_level)) {
            return;
        }
        
        // Add timestamp and level to context
        $context['timestamp'] = current_time('mysql');
        $context['level'] = $level;
        
        // Format the log message
        $formatted_message = self::format_message($message, $context);
        
        // Write to log file
        self::write_to_log($formatted_message);
        
        // Also log to WordPress debug.log if WP_DEBUG is enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[AEGE] ' . $formatted_message);
        }
    }
    
    /**
     * Log an emergency message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function emergency($message, $context = array()) {
        self::log(self::EMERGENCY, $message, $context);
    }
    
    /**
     * Log an alert message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function alert($message, $context = array()) {
        self::log(self::ALERT, $message, $context);
    }
    
    /**
     * Log a critical message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function critical($message, $context = array()) {
        self::log(self::CRITICAL, $message, $context);
    }
    
    /**
     * Log an error message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function error($message, $context = array()) {
        self::log(self::ERROR, $message, $context);
    }
    
    /**
     * Log a warning message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function warning($message, $context = array()) {
        self::log(self::WARNING, $message, $context);
    }
    
    /**
     * Log a notice message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function notice($message, $context = array()) {
        self::log(self::NOTICE, $message, $context);
    }
    
    /**
     * Log an info message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function info($message, $context = array()) {
        self::log(self::INFO, $message, $context);
    }
    
    /**
     * Log a debug message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     */
    public static function debug($message, $context = array()) {
        self::log(self::DEBUG, $message, $context);
    }
    
    /**
     * Check if a message should be logged based on log level
     *
     * @param string $message_level  Message log level
     * @param string $setting_level  Configured log level
     * @return bool
     */
    private static function should_log($message_level, $setting_level) {
        $levels = array(
            self::EMERGENCY => 0,
            self::ALERT     => 1,
            self::CRITICAL  => 2,
            self::ERROR     => 3,
            self::WARNING   => 4,
            self::NOTICE    => 5,
            self::INFO      => 6,
            self::DEBUG     => 7,
        );
        
        $message_level_value = isset($levels[$message_level]) ? $levels[$message_level] : 3;
        $setting_level_value = isset($levels[$setting_level]) ? $levels[$setting_level] : 3;
        
        return $message_level_value <= $setting_level_value;
    }
    
    /**
     * Format a log message
     *
     * @param string $message Log message
     * @param array  $context Additional context data
     * @return string
     */
    private static function format_message($message, $context = array()) {
        $timestamp = isset($context['timestamp']) ? $context['timestamp'] : current_time('mysql');
        $level = isset($context['level']) ? strtoupper($context['level']) : 'INFO';
        
        // Remove timestamp and level from context as they're already handled
        unset($context['timestamp'], $context['level']);
        
        // Format context data
        $context_string = '';
        if (!empty($context)) {
            $context_string = ' | Context: ' . wp_json_encode($context);
        }
        
        return sprintf(
            '[%s] [%s] %s%s',
            $timestamp,
            $level,
            $message,
            $context_string
        );
    }
    
    /**
     * Write a message to the log file
     *
     * @param string $message Formatted log message
     */
    private static function write_to_log($message) {
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/aege-logs';
        
        // Create log directory if it doesn't exist
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }
        
        $log_file = $log_dir . '/aege.log';
        
        // Check if log file exists and is larger than 5MB
        if (file_exists($log_file) && filesize($log_file) > 5 * 1024 * 1024) {
            // Rotate log file
            $rotated_file = $log_dir . '/aege.log.' . date('Y-m-d-H-i-s');
            rename($log_file, $rotated_file);
        }
        
        // Write to log file
        file_put_contents($log_file, $message . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get log file content
     *
     * @return string Log file content
     */
    public static function get_log_content() {
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/aege-logs/aege.log';
        
        if (file_exists($log_file)) {
            return file_get_contents($log_file);
        }
        
        return '';
    }
    
    /**
     * Clear log file
     */
    public static function clear_log() {
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/aege-logs/aege.log';
        
        if (file_exists($log_file)) {
            unlink($log_file);
        }
    }
    
    /**
     * Get log file size
     *
     * @return int Log file size in bytes
     */
    public static function get_log_size() {
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/aege-logs/aege.log';
        
        if (file_exists($log_file)) {
            return filesize($log_file);
        }
        
        return 0;
    }
}