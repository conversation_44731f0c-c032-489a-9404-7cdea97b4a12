<?php
/**
 * AEGE Uninstall
 *
 * Uninstalling AEGE deletes all database tables and options.
 *
 * @package AEGE
 */

// If uninstall.php is not called by WordPress, die
if (!defined('WP_UNINSTALL_PLUGIN')) {
    die;
}

// Include the activator class to access the drop_database_tables method
require_once plugin_dir_path(__FILE__) . 'includes/class-aege-activator.php';

// Drop database tables
AEGE_Activator::drop_database_tables();

// Delete plugin options
delete_option('aege_settings');
delete_option('aege_scorecard_data');

// Delete transients
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_aege_%'");
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_aege_%'");

// Delete custom post meta
$wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE '_aege_%'");

// Delete custom term meta
$wpdb->query("DELETE FROM {$wpdb->termmeta} WHERE meta_key LIKE '_aege_%'");

// Delete llms.txt file if it exists
$llms_file = ABSPATH . 'llms.txt';
if (file_exists($llms_file)) {
    unlink($llms_file);
}