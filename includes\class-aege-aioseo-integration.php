<?php
/**
 * AEGE All In One SEO Integration
 *
 * Integrates AEGE with All In One SEO's sitemap functionality
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once AEGE_PLUGIN_DIR . 'includes/class-aege-sitemap-helper.php';

/**
 * Add AEGE sitemap to All In One SEO
 */
add_filter('aioseo_sitemap_additional_pages', function($pages) {
    // Get all AEGE-enabled content
    $aege_posts = AEGE_Sitemap_Helper::get_aege_enabled_posts_for_aioseo();
    $aege_pages = AEGE_Sitemap_Helper::get_aege_enabled_pages();
    $aege_categories = AEGE_Sitemap_Helper::get_aege_enabled_categories();
    $aege_tags = AEGE_Sitemap_Helper::get_aege_enabled_tags();
    
    $has_content = !empty($aege_posts) || !empty($aege_pages) || !empty($aege_categories) || !empty($aege_tags);
    
    if (!$has_content) {
        return $pages;
    }
    
    // Add AEGE-enabled posts
    if (!empty($aege_posts)) {
        foreach ($aege_posts as $post) {
            $aege_url = trailingslashit(get_permalink($post->ID)) . 'llm/';
            
            $pages[$aege_url] = array(
                'loc' => $aege_url,
                'lastmod' => get_post_modified_time('c', true, $post->ID),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            );
        }
    }
    
    // Add AEGE-enabled pages
    if (!empty($aege_pages)) {
        foreach ($aege_pages as $page) {
            $aege_url = trailingslashit(get_permalink($page->ID)) . 'llm/';
            
            $pages[$aege_url] = array(
                'loc' => $aege_url,
                'lastmod' => get_post_modified_time('c', true, $page->ID),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            );
        }
    }
    
    // Add AEGE-enabled categories
    if (!empty($aege_categories)) {
        foreach ($aege_categories as $category) {
            $aege_url = trailingslashit(get_category_link($category->term_id)) . 'llm/';
            
            $pages[$aege_url] = array(
                'loc' => $aege_url,
                'lastmod' => date('c'), // Categories don't have a direct modification time
                'changefreq' => 'weekly',
                'priority' => '0.6'
            );
        }
    }
    
    // Add AEGE-enabled tags
    if (!empty($aege_tags)) {
        foreach ($aege_tags as $tag) {
            $aege_url = trailingslashit(get_tag_link($tag->term_id)) . 'llm/';
            
            $pages[$aege_url] = array(
                'loc' => $aege_url,
                'lastmod' => AEGE_Sitemap_Helper::get_last_modified_date_for_term($tag->term_id, 'post_tag'),
                'changefreq' => 'weekly',
                'priority' => '0.6'
            );
        }
    }
    
    return $pages;
});

/**
 * Clear All In One SEO sitemap cache when AEGE cache is cleared
 */
add_action('aege_cache_cleared', function() {
    // Clear All In One SEO cache if active
    if (defined('AIOSEO_VERSION')) {
        // Trigger All In One SEO cache clearing
        if (class_exists('AIOSEO\Classes\Cache')) {
            \AIOSEO\Classes\Cache::clear();
        }
    }
});

add_action('aege_object_cache_cleared', function($post_id) {
    // Clear All In One SEO cache if active
    if (defined('AIOSEO_VERSION')) {
        // Trigger All In One SEO cache clearing
        if (class_exists('AIOSEO\Classes\Cache')) {
            \AIOSEO\Classes\Cache::clear();
        }
    }
});
