<?php
class AEGE_Activator {

	public static function activate() {
		self::set_default_options();
		self::create_database_tables();
		flush_rewrite_rules();
		
		// Regenerate llms.txt file on activation
		if (class_exists('AEGE_Admin')) {
			$admin = AEGE_Admin::get_instance('aege', AEGE_VERSION);
			$admin->regenerate_llms_file();
		}
	}

	private static function set_default_options() {
		$default_options = array(
			'master_switch' => false,
			'post_types' => array('post' => '1', 'page' => '1'),
			'workflow' => 'automated_override',
			'complex_content' => 'strip',
			'llms_file' => 'suggest',
			'robots_file' => 'suggest_virtual',
			'ai_enhancement' => false,
			'ai_provider' => 'openai',
			'openai_api_key' => '',
			'openai_model' => 'gpt-5',
			'google_api_key' => '',
			'google_model' => 'gemini-2.5-pro',
			'openrouter_api_key' => '',
			'openrouter_model_string' => '',
			'prompt_template' => 'Analyze the following article content. Your task is to act as a world-class SEO and restructure it for Answer-Engine Optimization (AEO)...',
            'cache_strategy' => 'smart_cache',
            'seo_import_strategy' => 'enhance',
            'schema_enhancement' => 'full',
            'max_words' => 500,
            'include_meta' => true,
            'include_excerpts' => true,
            'include_taxonomies' => true,
            'detailed_content' => true
		);

		if ( false === get_option( 'aege_settings' ) ) {
			add_option( 'aege_settings', $default_options );
		}
	}
	
	/**
	 * Create database tables for AEGE caching system
	 */
	private static function create_database_tables() {
		global $wpdb;

		$table_name = $wpdb->prefix . 'aege_cache';

		// Check if table already exists
		if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
			// Table exists, check if it needs updates
			self::update_database_table_if_needed($table_name);
			return;
		}

		$charset_collate = $wpdb->get_charset_collate();

		$sql = "CREATE TABLE $table_name (
			id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
			object_id bigint(20) UNSIGNED NOT NULL,
			object_type varchar(20) NOT NULL,
			cache_key varchar(255) NOT NULL,
			content longtext,
			meta longtext,
			excerpts longtext,
			overview longtext,
			taxonomies longtext,
			title text,
			link varchar(255),
			sku varchar(255),
			price varchar(125),
			published datetime,
			modified datetime,
			last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY object_unique (object_id,object_type,cache_key),
			KEY object_id (object_id),
			KEY object_type (object_type),
			KEY published (published),
			KEY modified (modified)
		) $charset_collate;";

		require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

		// Create the table
		$result = $wpdb->query($sql);

		if ($result === false) {
			error_log('AEGE: Failed to create cache table: ' . $wpdb->last_error);
		}
	}

	/**
	 * Update database table structure if needed
	 */
	private static function update_database_table_if_needed($table_name) {
		global $wpdb;

		// Check if the unique key exists
		$key_exists = $wpdb->get_var("
			SELECT COUNT(*)
			FROM INFORMATION_SCHEMA.STATISTICS
			WHERE table_schema = DATABASE()
			AND table_name = '$table_name'
			AND index_name = 'object_unique'
		");

		// If the key doesn't exist, we can safely add it
		if (!$key_exists) {
			$wpdb->query("ALTER TABLE $table_name ADD UNIQUE KEY object_unique (object_id,object_type,cache_key)");
		}
	}
	
	/**
	 * Drop database tables for AEGE caching system (for complete uninstall)
	 */
	public static function drop_database_tables() {
		global $wpdb;
		
		$table_name = $wpdb->prefix . 'aege_cache';
		
		// Check if table exists before trying to drop it
		if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
			$wpdb->query("DROP TABLE IF EXISTS $table_name");
		}
	}
}