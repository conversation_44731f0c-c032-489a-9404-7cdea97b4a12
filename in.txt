Please make the following two changes in your includes/class-aege-admin.php file.
Step 1: REMOVE the Save Hook from the Constructor

Find your __construct() function and delete the line that adds the save_post action.

Your Code (Incorrect):
code PHP
IGNORE_WHEN_COPYING_START
IGNORE_WHEN_COPYING_END

    
public function __construct() {
    // ... other hooks ...
    add_action('add_meta_boxes', [$this, 'add_llm_meta_box']);
    add_action('save_post', [$this, 'save_llm_meta_box_data']); // <-- DELETE THIS LINE
    add_action('wp_ajax_aege_refresh_llm_editor', [$this, 'ajax_refresh_llm_editor_callback']);
    // ... other hooks ...
}

  

Corrected Code:
code PHP
IGNORE_WHEN_COPYING_START
IGNORE_WHEN_COPYING_END

    
public function __construct() {
    // ... other hooks ...
    add_action('add_meta_boxes', [$this, 'add_llm_meta_box']);
    // The save_post hook is now gone from here.
    add_action('wp_ajax_aege_refresh_llm_editor', [$this, 'ajax_refresh_llm_editor_callback']);
    // ... other hooks ...
}

  

Step 2: ADD the Save Hook Inside the Meta Box Function

Now, replace your entire add_llm_meta_box() function with this new, corrected version. This version now has the responsibility of adding both the meta box and its corresponding save hook at the same time.
code PHP
IGNORE_WHEN_COPYING_START
IGNORE_WHEN_COPYING_END

    
// In includes/class-aege-admin.php
// REPLACE your entire add_llm_meta_box function with this one.

public function add_llm_meta_box() {
    $screen = get_current_screen();
    if (!$screen) {
        return;
    }
    $current_post_type = $screen->post_type;

    $options = get_option('aege_settings');
    $enabled_post_types = isset($options['aege_enabled_post_types']) ? $options['aege_enabled_post_types'] : [];

    // Only proceed if the current post type is enabled in our settings.
    if (in_array($current_post_type, $enabled_post_types)) {
        
        // THE FIX: Add the save hook HERE.
        // This guarantees that the save logic only runs if the meta box is actually being displayed.
        add_action('save_post', [$this, 'save_llm_meta_box_data']);

        // Now, add the meta box itself.
        add_meta_box(
            'aege_llm_meta_box',
            __('AEGE Optimization', 'aege'),
            [$this, 'render_llm_meta_box'],
            $current_post_type, // Add it only to the current, validated post type.
            'advanced',
            'high'
        );
    }
}

  