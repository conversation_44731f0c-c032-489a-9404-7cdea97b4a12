<?php
/**
 * <PERSON>EGE SEOPress Integration
 *
 * Integrates AEGE with SEOPress's sitemap functionality
 *
 * @package AEGE
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/class-aege-sitemap-helper.php';

class AEGE_SEOPress_Integration {
    
    /**
     * Initialize the integration
     */
    public function __construct() {
        // Add rewrite rules for our sitemap
        add_action('init', array($this, 'add_rewrite_rules'), 10);
        
        // Handle direct sitemap requests
        add_action('init', array($this, 'maybe_generate_sitemap'), 999);
        
        // Clear sitemap cache when AEGE content changes
        add_action('aege_cache_cleared', array($this, 'clear_sitemap_cache'));
        add_action('aege_object_cache_cleared', array($this, 'clear_sitemap_cache'));
        add_action('save_post', array($this, 'clear_sitemap_cache_on_post_save'));
    }
    
    /**
     * Add rewrite rules for our sitemap
     */
    public function add_rewrite_rules() {
        // Add rule for aege-sitemap.xml
        add_rewrite_rule('^aege-sitemap\.xml$', 'index.php?aege_sitemap=1', 'top');
        
        // Add our custom query variable
        add_rewrite_tag('%aege_sitemap%', '([^&]+)');
    }
    
    /**
     * Handle direct sitemap requests
     */
    public function maybe_generate_sitemap() {
        // Check if this is a request for our sitemap
        $aege_sitemap = get_query_var('aege_sitemap');
        if (!empty($aege_sitemap)) {
            status_header(200);
            header('Content-Type: application/xml; charset=utf-8');
            echo $this->generate_sitemap();
            exit;
        }
    }
    
    /**
     * Generate the AEGE sitemap content (for direct access)
     */
    public function generate_sitemap() {
        // Check if AEGE is enabled
        $options = get_option('aege_settings');
        if (empty($options['master_switch'])) {
            return '';
        }
        
        // Get all AEGE-enabled content
        $aege_posts = AEGE_Sitemap_Helper::get_aege_enabled_posts();
        $aege_pages = AEGE_Sitemap_Helper::get_aege_enabled_pages();
        $aege_categories = AEGE_Sitemap_Helper::get_aege_enabled_categories();
        $aege_tags = AEGE_Sitemap_Helper::get_aege_enabled_tags();
        
        $has_content = !empty($aege_posts) || !empty($aege_pages) || !empty($aege_categories) || !empty($aege_tags);
        
        if (!$has_content) {
            return '';
        }
        
        // Start building the sitemap
        $xsl_url = esc_url(home_url('main-sitemap.xsl'));
        $sitemap_content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $sitemap_content .= "<?xml-stylesheet type=\"text/xsl\" href=\"{$xsl_url}\"?>\n";
        $sitemap_content .= "<urlset xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\" xsi:schemaLocation=\"http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd\">
";
        
        // Add each AEGE-enabled post to the sitemap
        if (!empty($aege_posts)) {
            foreach ($aege_posts as $post) {
                $aege_url = trailingslashit(get_permalink($post->ID)) . 'llm/';
                $lastmod = get_post_modified_time('c', true, $post->ID);
                $changefreq = 'weekly';
                $priority = '0.8';
                
                $sitemap_content .= "\t<url>\n";
                $sitemap_content .= "\t\t<loc>" . esc_url($aege_url) . "</loc>\n";
                $sitemap_content .= "\t\t<lastmod>" . esc_xml($lastmod) . "</lastmod>\n";
                $sitemap_content .= "\t\t<changefreq>" . esc_xml($changefreq) . "</changefreq>\n";
                $sitemap_content .= "\t\t<priority>" . esc_xml($priority) . "</priority>\n";
                $sitemap_content .= "\t</url>\n";
            }
        }
        
        // Add each AEGE-enabled page to the sitemap
        if (!empty($aege_pages)) {
            foreach ($aege_pages as $page) {
                $aege_url = trailingslashit(get_permalink($page->ID)) . 'llm/';
                $lastmod = get_post_modified_time('c', true, $page->ID);
                $changefreq = 'weekly';
                $priority = '0.8';
                
                $sitemap_content .= "\t<url>\n";
                $sitemap_content .= "\t\t<loc>" . esc_url($aege_url) . "</loc>\n";
                $sitemap_content .= "\t\t<lastmod>" . esc_xml($lastmod) . "</lastmod>\n";
                $sitemap_content .= "\t\t<changefreq>" . esc_xml($changefreq) . "</changefreq>\n";
                $sitemap_content .= "\t\t<priority>" . esc_xml($priority) . "</priority>\n";
                $sitemap_content .= "\t</url>\n";
            }
        }
        
        // Add each AEGE-enabled category to the sitemap
        if (!empty($aege_categories)) {
            foreach ($aege_categories as $category) {
                $aege_url = trailingslashit(get_category_link($category->term_id)) . 'llm/';
                // For categories, we'll use the current time as lastmod since there's no direct way to get it
                $lastmod = date('c');
                $changefreq = 'weekly';
                $priority = '0.6';
                
                $sitemap_content .= "\t<url>\n";
                $sitemap_content .= "\t\t<loc>" . esc_url($aege_url) . "</loc>\n";
                $sitemap_content .= "\t\t<lastmod>" . esc_xml($lastmod) . "</lastmod>\n";
                $sitemap_content .= "\t\t<changefreq>" . esc_xml($changefreq) . "</changefreq>\n";
                $sitemap_content .= "\t\t<priority>" . esc_xml($priority) . "</priority>\n";
                $sitemap_content .= "\t</url>\n";
            }
        }
        
        // Add each AEGE-enabled tag to the sitemap
        if (!empty($aege_tags)) {
            foreach ($aege_tags as $tag) {
                $aege_url = trailingslashit(get_tag_link($tag->term_id)) . 'llm/';
                $lastmod = AEGE_Sitemap_Helper::get_last_modified_date_for_term($tag->term_id, 'post_tag');
                $changefreq = 'weekly';
                $priority = '0.6';
                
                $sitemap_content .= "\t<url>\n";
                $sitemap_content .= "\t\t<loc>" . esc_url($aege_url) . "</loc>\n";
                $sitemap_content .= "\t\t<lastmod>" . esc_xml($lastmod) . "</lastmod>\n";
                $sitemap_content .= "\t\t<changefreq>" . esc_xml($changefreq) . "</changefreq>\n";
                $sitemap_content .= "\t\t<priority>" . esc_xml($priority) . "</priority>\n";
                $sitemap_content .= "\t</url>\n";
            }
        }
        
        $sitemap_content .= "</urlset>\n";
        
        return $sitemap_content;
    }
    
    /**
     * Clear sitemap cache when a post is saved
     */
    public function clear_sitemap_cache_on_post_save($post_id) {
        // Verify this is not an auto-save
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Verify this is a valid post type for AEGE
        $options = get_option('aege_settings');
        if (empty($options['post_types'])) {
            return;
        }
        
        $post = get_post($post_id);
        if (!in_array($post->post_type, array_keys($options['post_types']))) {
            return;
        }
        
        // Clear the sitemap cache
        $this->clear_sitemap_cache();
    }
    
    /**
     * Clear SEOPress sitemap cache
     */
    public function clear_sitemap_cache() {
        // Trigger SEOPress cache clearing
        do_action('seopress_flush_sitemaps');
        
        // Also clear rewrite rules so they can be regenerated
        flush_rewrite_rules();
    }
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        static $instance = null;
        if (null === $instance) {
            $instance = new self();
        }
        return $instance;
    }
}

// Initialize the integration
AEGE_SEOPress_Integration::get_instance();

/**
 * Add AEGE URLs to SEOPress sitemap
 */
add_filter('seopress_sitemaps_url_array', function($urls, $cpt) {
    // Only add to post sitemaps
    if ($cpt !== 'post') {
        return $urls;
    }
    
    // Get all AEGE-enabled posts
    $aege_posts = AEGE_Sitemap_Helper::get_aege_enabled_posts();
    
    foreach ($aege_posts as $post) {
        $aege_url = trailingslashit(get_permalink($post->ID)) . 'llm/';
        
        $urls[] = array(
            'loc' => $aege_url,
            'lastmod' => get_post_modified_time('c', true, $post->ID),
            'changefreq' => 'weekly',
            'priority' => '0.8'
        );
    }
    
    return $urls;
}, 10, 2);

/**
 * Add AEGE URLs to SEOPress page sitemap
 */
add_filter('seopress_sitemaps_url_array_page', function($urls) {
    // Get all AEGE-enabled pages
    $aege_pages = AEGE_Sitemap_Helper::get_aege_enabled_pages();
    
    foreach ($aege_pages as $page) {
        $aege_url = trailingslashit(get_permalink($page->ID)) . 'llm/';
        
        $urls[] = array(
            'loc' => $aege_url,
            'lastmod' => get_post_modified_time('c', true, $page->ID),
            'changefreq' => 'weekly',
            'priority' => '0.8'
        );
    }
    
    return $urls;
});

/**
 * Add AEGE URLs to SEOPress category sitemap
 */
add_filter('seopress_sitemaps_url_array_category', function($urls) {
    // Get all AEGE-enabled categories
    $aege_categories = AEGE_Sitemap_Helper::get_aege_enabled_categories();
    
    foreach ($aege_categories as $category) {
        $aege_url = trailingslashit(get_category_link($category->term_id)) . 'llm/';
        
        $urls[] = array(
            'loc' => $aege_url,
            'lastmod' => date('c'), // Categories don't have a direct modification time
            'changefreq' => 'weekly',
            'priority' => '0.6'
        );
    }
    
    return $urls;
});

/**
 * Add AEGE URLs to SEOPress tag sitemap
 */
add_filter('seopress_sitemaps_url_array_post_tag', function($urls) {
    // Get all AEGE-enabled tags
    $aege_tags = AEGE_Sitemap_Helper::get_aege_enabled_tags();
    
    foreach ($aege_tags as $tag) {
        $aege_url = trailingslashit(get_tag_link($tag->term_id)) . 'llm/';
        
        $urls[] = array(
            'loc' => $aege_url,
            'lastmod' => date('c'), // Tags don't have a direct modification time
            'changefreq' => 'weekly',
            'priority' => '0.6'
        );
    }
    
    return $urls;
});

/**
 * Clear SEOPress sitemap cache when AEGE cache is cleared
 */
add_action('aege_cache_cleared', function() {
    // Trigger SEOPress cache clearing
    do_action('seopress_flush_sitemaps');
});

add_action('aege_post_cache_cleared', function($post_id) {
    // Trigger SEOPress cache clearing
    do_action('seopress_flush_sitemaps');
});


