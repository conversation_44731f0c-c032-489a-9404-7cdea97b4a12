<?php
class AEGE_Sitemap_Helper {

    public static function get_aege_enabled_posts() {
        $options = get_option('aege_settings');
        if (empty($options['post_types'])) {
            return array();
        }

        $args = array(
            'post_type' => array_keys($options['post_types']),
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_aege_enabled',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );

        return get_posts($args);
    }

    

    public static function get_aege_enabled_categories() {
        $options = get_option('aege_settings');
        if (!isset($options['post_types']['post']) || $options['post_types']['post'] != 1) {
            return array();
        }

        return get_categories(array('hide_empty' => false));
    }

    public static function get_aege_enabled_tags() {
        $options = get_option('aege_settings');
        if (!isset($options['post_types']['post']) || $options['post_types']['post'] != 1) {
            return array();
        }

        return get_tags(array('hide_empty' => false));
    }

    public static function get_last_modified_date_for_term($term_id, $taxonomy) {
        $posts = get_posts(array(
            'tax_query' => array(
                array(
                    'taxonomy' => $taxonomy,
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            ),
            'posts_per_page' => 1,
            'orderby' => 'modified',
            'order' => 'DESC'
        ));

        if ($posts) {
            return get_post_modified_time('c', true, $posts[0]->ID);
        }

        return date('c');
    }

    public static function get_aege_sitemap_lastmod() {
        $sitemap_path = ABSPATH . 'aege-sitemap.xml';
        if (file_exists($sitemap_path)) {
            return date('c', filemtime($sitemap_path));
        }
        return date('c');
    }

    public static function get_aege_enabled_posts_for_yoast() {
        $posts = self::get_aege_enabled_posts();
        $filtered_posts = array();
        foreach ($posts as $post) {
            if (!self::is_yoast_noindex($post->ID)) {
                $filtered_posts[] = $post;
            }
        }
        return $filtered_posts;
    }

    public static function is_yoast_noindex($post_id) {
        if (!class_exists('WPSEO_Meta')) {
            return false;
        }
        $robots = WPSEO_Meta::get_value('meta-robots-noindex', $post_id);
        return $robots === '1';
    }

    public static function get_aege_enabled_posts_for_aioseo() {
        $posts = self::get_aege_enabled_posts();
        $filtered_posts = array();
        foreach ($posts as $post) {
            if (!self::is_aioseo_noindex($post->ID)) {
                $filtered_posts[] = $post;
            }
        }
        return $filtered_posts;
    }

    public static function is_aioseo_noindex($post_id) {
        if (!defined('AIOSEO_VERSION')) {
            return false;
        }
        $noindex = get_post_meta($post_id, '_aioseo_robots_noindex', true);
        if ($noindex === 'on' || $noindex === '1') {
            return true;
        }
        $nofollow = get_post_meta($post_id, '_aioseo_robots_nofollow', true);
        if ($nofollow === 'on' || $nofollow === '1') {
            return true;
        }
        $custom_robots = get_post_meta($post_id, '_aioseo_robots', true);
        if (!empty($custom_robots) && is_array($custom_robots)) {
            if (in_array('noindex', $custom_robots) || in_array('nofollow', $custom_robots)) {
                return true;
            }
        }
        return false;
    }
}