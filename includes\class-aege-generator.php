<?php
class AEGE_Generator {

    private $options;
    private $aege_post_noindex = false;
    private $content_cleaner;

    public function __construct() {
        $this->options = get_option('aege_settings');
        $this->content_cleaner = new AEGE_Content_Cleaner();
    }

    /**
     * Main render function. Checks cache, generates if needed, caches, and serves the page.
     */
    public function render_aege_page() {
        // Check if the master switch is enabled
        if (empty($this->options['master_switch'])) {
            AEGE_Logger::debug('AEGE master switch is off, returning 404');
            // If master switch is off, return a 404
            status_header(404);
            echo '<!DOCTYPE html><html><head><title>Not Found</title></head><body><h1>404 - Not Found</h1><p>This content is not available.</p></body></html>';
            exit;
        }
        
        $queried_object = get_queried_object();
        AEGE_Logger::debug('Rendering AEGE page', array(
            'queried_object_type' => get_class($queried_object),
            'queried_object_id' => $queried_object->ID ?? $queried_object->term_id ?? 'unknown'
        ));
        
        // Handle posts and pages
        if (is_a($queried_object, 'WP_Post')) {
            $this->render_post_page($queried_object);
        }
        // Handle terms
        else if (is_a($queried_object, 'WP_Term')) {
            $this->render_term_page($queried_object);
        }
    }
    
    /**
     * Render AEGE page for a post or page
     */
    private function render_post_page($post) {
        $post_id = $post->ID;
        if ( ! $post_id ) {
            AEGE_Logger::warning('Post ID not found in render_post_page');
            return; // Should not happen on a singular page, but a good safeguard.
        }

        AEGE_Logger::debug('Rendering post page', array(
            'post_id' => $post_id,
            'post_title' => $post->post_title
        ));

        // Check if this post should be noindex
        if ($this->should_noindex_post($post_id)) {
            AEGE_Logger::debug('Post should be noindex, returning 404', array('post_id' => $post_id));
            status_header(404);
            echo '<!DOCTYPE html><html><head><title>Not Found</title></head><body><h1>404 - Not Found</h1><p>This content is not available.</p></body></html>';
            exit;
        }

        // 1. Check cache first
        $cached_html = AEGE_Cache_Manager::get_cache($post_id, 'post');
        if ($cached_html) {
            AEGE_Logger::debug('Serving cached content', array('post_id' => $post_id));
            echo $cached_html;
            exit;
        }

        // --- Start Generation Process if not cached ---
        ob_start(); // Start output buffering to capture all HTML

        try {
            if ( ! $post ) {
                throw new Exception('Post not found.');
            }

            // Get the base content (either custom meta or main content)
            $content = $this->get_content_source($post);

            // AI Enhancement Hook (Future Feature)
            if ( ! empty($this->options['ai_enhancement']) ) {
                // $content = AEGE_Api_Handler::enhance_content($content); // Placeholder for future API call
                AEGE_Logger::debug('AI enhancement enabled but not implemented yet', array('post_id' => $post_id));
            }

            // Sanitize content and rewrite internal links
            $processed_content = $this->process_content($content);

            // Generate summaries if enabled
            $summary_content = '';
            if ( ! empty($this->options['enable_summaries']) ) {
                AEGE_Logger::debug('Generating summaries', array('post_id' => $post_id));
                $summary_generator = new AEGE_Summary_Generator();
                $takeaways = $summary_generator->generate_key_takeaways($processed_content);
                $summary_content = $summary_generator->format_takeaways_html($takeaways);
            }

            // Get schema data (imports from other plugins or generates its own)
            $schema_data = $this->get_schema($post, $processed_content);

            // Include the template file, passing variables to it
            $this->include_template($post, $processed_content, $schema_data, $summary_content);

        } catch (Exception $e) {
            $error_message = $e->getMessage();
            if (empty($error_message)) {
                $error_message = 'Unknown error occurred during AEGE page generation';
            }
            
            AEGE_Logger::error('Error generating AEGE page', array(
                'post_id' => $post_id,
                'error_message' => $error_message,
                'error_trace' => $e->getTraceAsString()
            ));
            // In case of an error, just show a simple error message.
            // A more robust system might log this error.
            echo "<!-- AEGE Generation Error: " . esc_html($error_message) . " -->";
        }

        // 2. Capture the output
        $generated_html = ob_get_clean();

        // 3. Save the captured HTML to the cache
        AEGE_Cache_Manager::set_cache($post_id, 'post', $generated_html);

        // 4. Serve the generated HTML
        AEGE_Logger::debug('Successfully generated and served AEGE page', array(
            'post_id' => $post_id,
            'content_length' => strlen($generated_html)
        ));
        echo $generated_html;
        exit;
    }
    
    /**
     * Render AEGE page for a category
     */
    private function render_term_page($term) {
        $term_id = $term->term_id;
        $taxonomy = $term->taxonomy;

        // 1. Check cache first
        $cached_html = AEGE_Cache_Manager::get_cache($term_id, 'term_' . $taxonomy);
        if ($cached_html) {
            echo $cached_html;
            exit;
        }

        // --- Start Generation Process if not cached ---
        ob_start(); // Start output buffering to capture all HTML

        try {
            // Get posts in this term
            $posts = get_posts(array(
                'tax_query' => array(
                    array(
                        'taxonomy' => $taxonomy,
                        'field'    => 'term_id',
                        'terms'    => $term_id,
                    ),
                ),
                'posts_per_page' => 50, // Limit to 50 posts
                'meta_query' => array(
                    array(
                        'key' => '_aege_enabled',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            ));
            
            // Get term description
            $description = term_description($term_id, $taxonomy);
            
            // Get schema data for term
            $schema_data = $this->get_term_schema($term, $posts);
            
            // Include the template file, passing variables to it
            $aege_term = $term;
            $aege_posts = $posts;
            $aege_description = $description;
            $aege_schema = $schema_data;
            
            $template_path = AEGE_PLUGIN_DIR . 'templates/aege-term-template.php';
            if (file_exists($template_path)) {
                include $template_path;
            } else {
                // Fallback to main template
                $this->include_template(null, $description, $schema_data);
            }

        } catch (Exception $e) {
            $error_message = $e->getMessage();
            if (empty($error_message)) {
                $error_message = 'Unknown error occurred during AEGE term page generation';
            }
            
            AEGE_Logger::error('Error generating AEGE page for term', array(
                'term_id' => $term_id,
                'taxonomy' => $taxonomy,
                'error_message' => $error_message,
                'error_trace' => $e->getTraceAsString()
            ));
            // In case of an error, just show a simple error message.
            echo "<!-- AEGE Generation Error for Term: " . esc_html($error_message) . " -->";
        }

        // 2. Capture the output
        $generated_html = ob_get_clean();

        // 3. Save the captured HTML to the cache
        AEGE_Cache_Manager::set_cache($term_id, 'term_' . $taxonomy, $generated_html);

        // 4. Serve the generated HTML
        echo $generated_html;
        exit;
    }

    /**
     * Determines the source content based on plugin settings.
     */
    private function get_content_source($post) {
        $workflow = isset($this->options['workflow']) ? $this->options['workflow'] : 'automated_override';
        if ($workflow === 'automated_override') {
            $custom_content = get_post_meta($post->ID, '_aege_custom_content', true);
            if (!empty($custom_content)) {
                return $custom_content;
            }
        }
        // Apply 'the_content' filter to process shortcodes etc. before we strip them
        return apply_filters('the_content', $post->post_content);
    }

    /**
     * Cleans content and rewrites internal links.
     */
    private function process_content($content) {
        // Use our content cleaner instead of wp_kses_post to preserve HTML structure
        $content = $this->content_cleaner->clean($content);
        
        // Rewrite internal links
        $content = preg_replace_callback('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', array($this, 'process_internal_links_callback'), $content);

        return $content;
    }

    /**
     * Callback function for rewriting internal links.
     */
    public function process_internal_links_callback($matches) {
        $original_link_tag = $matches[0];
        $url = $matches[1];
        
        $site_url = site_url();

        // Check if it's an internal link
        if (strpos($url, $site_url) !== false) {
            // Check if it's a post link
            $post_id = url_to_postid($url);
            if ($post_id && get_post_meta($post_id, '_aege_enabled', true)) {
                $aege_url = trailingslashit($url) . 'llm/';
                return '<a href="' . esc_url($aege_url) . '"';
            }
            
            // Check if it's a category link
            $category_id = get_category_by_slug(basename(rtrim($url, '/')));
            if ($category_id && isset($this->options['post_types']['post']) && $this->options['post_types']['post'] == 1) {
                $aege_url = trailingslashit($url) . 'llm/';
                return '<a href="' . esc_url($aege_url) . '"';
            }
            
            // Check if it's a tag link
            $tag = get_term_by('slug', basename(rtrim($url, '/')), 'post_tag');
            if ($tag && isset($this->options['post_types']['post']) && $this->options['post_types']['post'] == 1) {
                $aege_url = trailingslashit($url) . 'llm/';
                return '<a href="' . esc_url($aege_url) . '"';
            }
        }
        
        // Return the original link if it's not an internal AEGE-enabled link
        return $original_link_tag;
    }

    /**
     * Generates Schema.org JSON-LD data for posts.
     */
    private function get_schema($post, $content) {
        // Get SEO integration settings
        $seo_import_strategy = isset($this->options['seo_import_strategy']) ? $this->options['seo_import_strategy'] : 'enhance';
        $schema_enhancement = isset($this->options['schema_enhancement']) ? $this->options['schema_enhancement'] : 'full';
        
        // If strategy is to replace, skip importing and generate our own
        if ($seo_import_strategy === 'replace') {
            return $this->generate_aege_schema($post, $content);
        }
        
        // Try to import schema from SEO plugins
        $imported_schema = $this->import_schema_from_seo_plugins($post);
        
        // If we couldn't import or strategy is to preserve, return what we have
        if (!$imported_schema || $seo_import_strategy === 'preserve') {
            $main_schema = $imported_schema ? $imported_schema : $this->generate_aege_schema($post, $content);
        } else {
            // If we're here, we're enhancing the imported schema
            $main_schema = $imported_schema;
            
            // Ensure we have the basic required fields
            if (!isset($main_schema['@context'])) {
                $main_schema['@context'] = 'https://schema.org';
            }
            
            // Apply enhancements based on settings
            if ($schema_enhancement === 'full' || $schema_enhancement === 'minimal') {
                // Add mainEntityOfPage if not present
                if (!isset($main_schema['mainEntityOfPage'])) {
                    $main_schema['mainEntityOfPage'] = array(
                        '@type' => 'WebPage',
                        '@id' => get_permalink($post->ID),
                    );
                }
                
                // Add article body if not present (full enhancement only)
                if ($schema_enhancement === 'full' && !isset($main_schema['articleBody'])) {
                    $main_schema['articleBody'] = wp_strip_all_tags($content);
                }
            }
        }
        
        // Detect additional schemas in content
        $detected_schemas = $this->content_cleaner->detect_and_generate_schemas($content, $post->ID);
        
        // If we have detected schemas, create a graph structure
        if (!empty($detected_schemas)) {
            // Create a graph with the main schema and detected schemas
            $schema_graph = array(
                '@context' => 'https://schema.org',
                '@graph' => array_merge(array($main_schema), $detected_schemas)
            );
            
            return $schema_graph;
        }
        
        // Return just the main schema if no additional schemas detected
        return $main_schema;
    }
    
    /**
     * Generates Schema.org JSON-LD data for categories.
     */
    private function get_term_schema($term, $posts) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'CollectionPage',
            'name' => $term->name,
            'description' => $term->description,
            'url' => get_term_link($term),
        );
        
        // Add mainEntityOfPage
        $schema['mainEntityOfPage'] = array(
            '@type' => 'WebPage',
            '@id' => get_term_link($term),
        );
        
        // Add publisher info
        $schema['publisher'] = array(
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => get_site_icon_url(),
            ),
        );
        
        return $schema;
    }

    /**
     * Generate AEGE-specific schema for posts
     */
    private function generate_aege_schema($post, $content) {
        // Try to get the schema article type from Yoast settings if available
        $article_type = 'Article'; // Default fallback
        
        if (class_exists('WPSEO_Meta')) {
            $schema_article_type = WPSEO_Meta::get_value('schema_article_type', $post->ID);
            // If we have a specific schema article type from Yoast, use it
            if (!empty($schema_article_type) && $schema_article_type !== 'None') {
                $article_type = $schema_article_type;
            } else if ($post->post_type === 'post') {
                // For posts, use Article (not NewsArticle) to match Yoast default
                $article_type = 'Article';
            }
        } else {
            // If Yoast is not available, use Article for posts
            if ($post->post_type === 'post') {
                $article_type = 'Article';
            }
        }
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => $article_type,
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => get_permalink($post->ID),
            ),
            'headline' => get_the_title($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $post->post_author),
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_site_icon_url(),
                ),
            ),
            'description' => get_the_excerpt($post->ID),
            'articleBody' => wp_strip_all_tags($content),
        );

        return $schema;
    }

    /**
     * Import schema data from popular SEO plugins.
     */
    private function import_schema_from_seo_plugins($post) {
        // Check for Yoast SEO
        if (class_exists('WPSEO_Meta')) {
            return $this->import_from_yoast_seo($post);
        }
        
        // Check for Rank Math
        if (class_exists('RankMath')) {
            return $this->import_from_rank_math($post);
        }
        
        // Check for SEOPress
        if (function_exists('seopress_activation')) {
            return $this->import_from_seopress($post);
        }
        
        // No compatible SEO plugin found
        return false;
    }

    /**
     * Import schema data from Yoast SEO.
     */
    private function import_from_yoast_seo($post) {
        // Check if Yoast is set to noindex this post
        $robots = WPSEO_Meta::get_value('meta-robots-noindex', $post->ID);
        if ($robots === '1') {
            // Set a flag that this post should be noindex
            $this->aege_post_noindex = true;
        }
        
        // Try to get the actual schema data that Yoast generates
        // This is the proper way to get Yoast's schema data
        if (class_exists('WPSEO_Schema_Generator')) {
            try {
                // Get the schema context
                $schema_context = new WPSEO_Schema_Context();
                
                // Create schema generator
                $schema_generator = new WPSEO_Schema_Generator($schema_context);
                
                // Generate the schema data
                $schema_data = $schema_generator->generate();
                
                // If we got schema data, return it
                if (!empty($schema_data)) {
                    return $schema_data;
                }
            } catch (Exception $e) {
                // If there's an error, fall back to manual generation
            }
        }
        
        // Fallback: Try to get individual meta values from Yoast and build schema
        // Get title
        $title = WPSEO_Meta::get_value('title', $post->ID);
        if (empty($title)) {
            $title = get_the_title($post->ID);
        }
        
        // Get meta description
        $meta_description = WPSEO_Meta::get_value('metadesc', $post->ID);
        if (empty($meta_description)) {
            $meta_description = get_the_excerpt($post->ID);
        }
        
        // Try to get the schema article type from Yoast settings
        $schema_article_type = WPSEO_Meta::get_value('schema_article_type', $post->ID);
        $webpage_type = WPSEO_Meta::get_value('schema_page_type', $post->ID);
        
        $article_type = 'Article'; // Default fallback
        $page_type = 'WebPage'; // Default fallback
        
        // If we have a specific schema article type from Yoast, use it
        if (!empty($schema_article_type) && $schema_article_type !== 'None') {
            $article_type = $schema_article_type;
        } else if ($post->post_type === 'post') {
            // For posts, use Article to match Yoast default
            $article_type = 'Article';
        }
        
        // If we have a specific page type from Yoast, use it
        if (!empty($webpage_type) && $webpage_type !== 'None') {
            $page_type = $webpage_type;
        }
        
        // For posts, we typically want Article schema
        if ($post->post_type === 'post') {
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => $article_type,
                'mainEntityOfPage' => array(
                    '@type' => $page_type,
                    '@id' => get_permalink($post->ID),
                ),
                'headline' => get_the_title($post->ID),
                'datePublished' => get_the_date('c', $post->ID),
                'dateModified' => get_the_modified_date('c', $post->ID),
                'author' => array(
                    '@type' => 'Person',
                    'name' => get_the_author_meta('display_name', $post->post_author),
                ),
                'publisher' => array(
                    '@type' => 'Organization',
                    'name' => get_bloginfo('name'),
                    'logo' => array(
                        '@type' => 'ImageObject',
                        'url' => get_site_icon_url(),
                    ),
                ),
                'description' => $meta_description,
            );
        } else {
            // For pages and other post types, use WebPage schema
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => $page_type,
                'mainEntityOfPage' => array(
                    '@type' => 'WebPage',
                    '@id' => get_permalink($post->ID),
                ),
                'headline' => get_the_title($post->ID),
                'datePublished' => get_the_date('c', $post->ID),
                'dateModified' => get_the_modified_date('c', $post->ID),
                'author' => array(
                    '@type' => 'Person',
                    'name' => get_the_author_meta('display_name', $post->post_author),
                ),
                'publisher' => array(
                    '@type' => 'Organization',
                    'name' => get_bloginfo('name'),
                    'logo' => array(
                        '@type' => 'ImageObject',
                        'url' => get_site_icon_url(),
                    ),
                ),
                'description' => $meta_description,
            );
        }
        
        return $schema;
    }

    /**
     * Import schema data from Rank Math.
     */
    private function import_from_rank_math($post) {
        // Check if Rank Math is set to noindex this post
        $robots = get_post_meta($post->ID, 'rank_math_robots', true);
        if (is_array($robots) && in_array('noindex', $robots)) {
            // Set a flag that this post should be noindex
            $this->aege_post_noindex = true;
        }
        
        // Try to get the actual schema data that Rank Math generates
        if (function_exists('rank_math_get_schemas')) {
            $schemas = rank_math_get_schemas($post->ID);
            if (!empty($schemas)) {
                return $schemas;
            }
        }
        
        // Fallback to manual generation
        return $this->generate_aege_schema($post, $post->post_content);
    }

    /**
     * Import schema data from SEOPress.
     */
    private function import_from_seopress($post) {
        // Check if SEOPress is set to noindex this post
        $robots = get_post_meta($post->ID, '_seopress_robots_index', true);
        if ($robots === 'yes') {
            // Set a flag that this post should be noindex
            $this->aege_post_noindex = true;
        }
        
        // Try to get the actual schema data that SEOPress generates
        if (function_exists('seopress_get_json_schema')) {
            $schemas = seopress_get_json_schema($post->ID);
            if (!empty($schemas)) {
                return $schemas;
            }
        }
        
        // Fallback to manual generation
        return $this->generate_aege_schema($post, $post->post_content);
    }

    /**
     * Check if a post should be noindex based on SEO plugin settings
     */
    private function should_noindex_post($post_id) {
        // Check Yoast SEO
        if (class_exists('WPSEO_Meta')) {
            $robots = WPSEO_Meta::get_value('meta-robots-noindex', $post_id);
            if ($robots === '1') {
                return true;
            }
        }
        
        // Check Rank Math
        if (class_exists('RankMath')) {
            $robots = get_post_meta($post_id, 'rank_math_robots', true);
            if (is_array($robots) && in_array('noindex', $robots)) {
                return true;
            }
        }
        
        // Check SEOPress
        if (function_exists('seopress_activation')) {
            $robots = get_post_meta($post_id, '_seopress_robots_index', true);
            if ($robots === 'yes') {
                return true;
            }
        }
        
        // Check All In One SEO
        if (defined('AIOSEO_VERSION')) {
            $robots = get_post_meta($post_id, '_aioseo_robots_noindex', true);
            if ($robots === 'on') {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Includes the AEGE template file.
     */
    /**
     * Include the AEGE template file
     */
    private function include_template($post, $content, $schema_data, $summary_content = '') {
        // Make variables available to the template file
        $aege_post = $post;
        $aege_content = $content;
        $aege_schema = $schema_data;
        $aege_summary = $summary_content;

        $template_path = AEGE_PLUGIN_DIR . 'templates/aege-template.php';
        if (file_exists($template_path)) {
            include $template_path;
        }
    }
}
